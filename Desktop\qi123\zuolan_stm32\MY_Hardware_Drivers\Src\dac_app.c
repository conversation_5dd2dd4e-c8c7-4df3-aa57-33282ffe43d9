/**
 * @file dac_app.c
 * @brief DAC直流电压输出模块
 * @details 实现DAC输出0-3.3V直流电压
 * <AUTHOR>
 * @date 2025-08-01
 */

#include "dac_app.h"
#include "dac.h"
#include "my_usart.h"

// --- 配置常量 ---
#define DAC_VREF_MV 3300             // DAC参考电压(mV)
#define DAC_MAX_VALUE 4095           // 12位DAC最大数字值

// --- 私有变量 ---
static uint16_t current_voltage_mv = 0;  // 当前输出电压(mV)

/**
 * @brief 初始化DAC模块
 * @details 设置默认输出电压为0V
 */
void DAC_Init(void)
{
    DAC_SetVoltage(100);  // 默认输出100mV
    my_printf(&huart1, "DAC Init OK: %umV\r\n", current_voltage_mv);
}

/**
 * @brief 设置DAC输出电压
 * @param voltage_mv 输出电压(mV)，范围0-3300mV
 * @return 实际设置的电压值(mV)
 */
uint16_t DAC_SetVoltage(uint16_t voltage_mv)
{
    // 限制电压范围
    if (voltage_mv > DAC_VREF_MV) voltage_mv = DAC_VREF_MV;

    // 转换为DAC数字值
    uint32_t dac_value = (uint32_t)voltage_mv * DAC_MAX_VALUE / DAC_VREF_MV;
	// 启动DAC输出
    HAL_DAC_Start(&hdac, DAC_CHANNEL_1);

    // 设置DAC输出值
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_value);



    // 计算并保存实际电压值
    current_voltage_mv = (uint16_t)(dac_value * DAC_VREF_MV / DAC_MAX_VALUE);

    return current_voltage_mv;
}

/**
 * @brief 获取当前输出电压
 * @return 当前输出电压(mV)
 */
uint16_t DAC_GetVoltage(void)
{
    return current_voltage_mv;
}

/**
 * @brief 直接设置DAC原始值
 * @param dac_raw_value DAC原始值(0-4095)
 * @return 实际设置的电压值(mV)
 */
uint16_t DAC_SetRawValue(uint16_t dac_raw_value)
{
    // 限制DAC原始值范围
    if (dac_raw_value > DAC_MAX_VALUE) dac_raw_value = DAC_MAX_VALUE;

    // 启动DAC输出
    HAL_DAC_Start(&hdac, DAC_CHANNEL_1);

    // 直接设置DAC原始值
    HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_raw_value);

    // 计算并保存对应的电压值
    current_voltage_mv = (uint16_t)((uint32_t)dac_raw_value * DAC_VREF_MV / DAC_MAX_VALUE);

    return current_voltage_mv;
}

/**
 * @brief 获取当前DAC原始值
 * @return 当前DAC原始值(0-4095)
 */
uint16_t DAC_GetRawValue(void)
{
    return (uint16_t)((uint32_t)current_voltage_mv * DAC_MAX_VALUE / DAC_VREF_MV);
}

