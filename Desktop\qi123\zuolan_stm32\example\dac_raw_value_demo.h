/**
 * @file dac_raw_value_demo.h
 * @brief DAC原始值操作演示头文件
 * @details 提供DAC原始值操作演示的函数声明
 * <AUTHOR>
 * @date 2025-08-02
 */

#ifndef __DAC_RAW_VALUE_DEMO_H
#define __DAC_RAW_VALUE_DEMO_H

#include "stm32f4xx_hal.h"

/**
 * @brief 演示DAC原始值的变化过程
 * @details 详细展示每次步进时DAC原始值的变化
 * @param None
 * @retval None
 */
void demo_dac_raw_value_changes(void);

/**
 * @brief 演示不同频率下的DAC原始值
 * @details 展示在不同频率下，相同幅度对应的不同DAC原始值
 * @param None
 * @retval None
 */
void demo_dac_raw_value_different_frequencies(void);

/**
 * @brief 演示DAC原始值与电压的转换关系
 * @details 展示DAC原始值和实际电压之间的转换关系
 * @param None
 * @retval None
 */
void demo_dac_raw_to_voltage_conversion(void);

/**
 * @brief 运行所有DAC原始值演示
 * @details 运行所有相关的演示
 * @param None
 * @retval None
 */
void run_all_dac_raw_value_demos(void);

#endif // __DAC_RAW_VALUE_DEMO_H
