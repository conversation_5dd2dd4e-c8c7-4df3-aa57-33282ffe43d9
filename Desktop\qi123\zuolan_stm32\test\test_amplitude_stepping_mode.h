/**
 * @file test_amplitude_stepping_mode.h
 * @brief 幅度步进模式功能测试头文件
 * @details 提供幅度步进模式功能测试的函数声明
 * <AUTHOR>
 * @date 2025-08-02
 */

#ifndef __TEST_AMPLITUDE_STEPPING_MODE_H
#define __TEST_AMPLITUDE_STEPPING_MODE_H

#include "stm32f4xx_hal.h"

/**
 * @brief 测试幅度步进模式基本功能
 * @details 测试进入/退出幅度步进模式的基本功能
 * @param None
 * @retval None
 */
void test_amplitude_stepping_basic(void);

/**
 * @brief 测试不同频率下的幅度步进
 * @details 测试在不同频率下进入幅度步进模式的效果
 * @param None
 * @retval None
 */
void test_amplitude_stepping_frequencies(void);

/**
 * @brief 测试幅度步进边界条件
 * @details 测试幅度步进的上下限边界条件
 * @param None
 * @retval None
 */
void test_amplitude_stepping_boundaries(void);

/**
 * @brief 测试按键C的查表和DAC修改功能
 * @details 专门测试按键C进入模式时的查表和DAC输出修改功能
 * @param None
 * @retval None
 */
void test_key_c_lookup_and_dac_modification(void);

/**
 * @brief 测试DAC值的精确性
 * @details 验证查表得到的DAC值是否与预期一致
 * @param None
 * @retval None
 */
void test_dac_value_accuracy(void);

/**
 * @brief 运行所有幅度步进模式测试
 * @details 运行所有相关的测试用例
 * @param None
 * @retval None
 */
void run_all_amplitude_stepping_tests(void);

#endif // __TEST_AMPLITUDE_STEPPING_MODE_H
