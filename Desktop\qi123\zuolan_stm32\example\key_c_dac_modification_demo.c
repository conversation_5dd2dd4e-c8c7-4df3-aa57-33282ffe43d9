/**
 * @file key_c_dac_modification_demo.c
 * @brief 按键C查表和DAC修改功能演示
 * @details 专门演示按键C进入幅度步进模式时的查表和DAC输出修改功能
 * <AUTHOR>
 * @date 2025-08-02
 */

#include "key_c_dac_modification_demo.h"
#include "hmi_key_handler.h"
#include "my_usart.h"
#include "dac_amplitude_calibration.h"
#include "AD9959.h"
#include "stm32f4xx_hal.h"

/**
 * @brief 演示按键C的完整查表和DAC修改过程
 * @details 详细展示按键C按下后的每个步骤
 * @param None
 * @retval None
 */
void demo_key_c_complete_process(void)
{
    my_printf(&huart1, "\r\n=== 按键C完整过程演示 ===\r\n");
    
    // 初始化
    my_printf(&huart1, "1. 初始化系统...\r\n");
    dac_calibration_init();
    AD9959_Init();
    
    // 设置一个支持的频率
    uint32_t demo_frequency = 1000; // 1kHz
    my_printf(&huart1, "2. 设置DDS频率为%luHz...\r\n", demo_frequency);
    HMI_Set_DDS_Frequency(demo_frequency);
    
    // 显示当前状态
    my_printf(&huart1, "3. 显示当前状态:\r\n");
    my_printf(&huart1, "   DDS频率: %luHz\r\n", HMI_Get_DDS_Frequency());
    my_printf(&huart1, "   当前DAC电压: %umV\r\n", HMI_Get_DAC_Voltage());
    my_printf(&huart1, "   幅度步进模式: %s\r\n", 
             HMI_Get_Amplitude_Stepping_Mode() ? "启用" : "禁用");
    
    // 手动查询校准表，展示查表过程
    my_printf(&huart1, "4. 手动查询校准表(1.0V):\r\n");
    uint16_t target_amplitude = 10; // 1.0V
    uint16_t expected_dac = dac_calibration_get_dac_value(demo_frequency, target_amplitude);
    if (expected_dac > 0) {
        uint16_t expected_voltage = (uint16_t)((float)expected_dac * 3300.0f / 4095.0f);
        my_printf(&huart1, "   查表结果: DAC原始值=%u, 转换电压=%umV\r\n", 
                 expected_dac, expected_voltage);
    } else {
        my_printf(&huart1, "   查表失败: 无对应数据\r\n");
        return;
    }
    
    // 记录按键C前的状态
    uint16_t before_dac = HMI_Get_DAC_Voltage();
    my_printf(&huart1, "5. 按键C前状态记录:\r\n");
    my_printf(&huart1, "   DAC电压: %umV\r\n", before_dac);
    
    // 模拟按键C按下
    my_printf(&huart1, "6. 模拟按键C按下...\r\n");
    my_printf(&huart1, "   执行查表和DAC修改过程:\r\n");
    
    extern unsigned char USART_RX_BUF[];
    extern unsigned short USART_RX_STA;
    
    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001;
    HMI_Key_Process(); // 这里会输出详细的查表和修改过程
    
    HAL_Delay(1000);
    
    // 验证结果
    my_printf(&huart1, "7. 验证按键C执行结果:\r\n");
    uint16_t after_dac = HMI_Get_DAC_Voltage();
    
    if (HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "   ✓ 成功进入幅度步进模式\r\n");
        my_printf(&huart1, "   步进频率: %uHz\r\n", HMI_Get_Stepping_Frequency());
        my_printf(&huart1, "   当前幅度: %u.%uV\r\n", 
                 HMI_Get_Stepping_Amplitude()/10, HMI_Get_Stepping_Amplitude()%10);
        my_printf(&huart1, "   DAC变化: %umV → %umV\r\n", before_dac, after_dac);
        
        // 检查DAC是否按预期修改
        if (abs((int)after_dac - (int)expected_voltage) <= 5) {
            my_printf(&huart1, "   ✓ DAC输出符合校准表预期\r\n");
        } else {
            my_printf(&huart1, "   ⚠ DAC输出与预期有差异\r\n");
            my_printf(&huart1, "     预期: %umV, 实际: %umV\r\n", expected_voltage, after_dac);
        }
    } else {
        my_printf(&huart1, "   ✗ 未能进入幅度步进模式\r\n");
    }
    
    // 退出模式
    my_printf(&huart1, "8. 退出幅度步进模式...\r\n");
    USART_RX_BUF[0] = 'D';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    
    my_printf(&huart1, "\r\n=== 按键C完整过程演示完成 ===\r\n");
}

/**
 * @brief 演示多个频率下的按键C功能
 * @details 在不同频率下演示按键C的查表和DAC修改效果
 * @param None
 * @retval None
 */
void demo_key_c_multiple_frequencies(void)
{
    my_printf(&huart1, "\r\n=== 多频率按键C功能演示 ===\r\n");
    
    // 测试频率列表
    uint32_t frequencies[] = {500, 1000, 1500, 2000, 2500};
    int num_freq = sizeof(frequencies) / sizeof(frequencies[0]);
    
    for (int i = 0; i < num_freq; i++) {
        my_printf(&huart1, "\r\n--- 频率: %luHz ---\r\n", frequencies[i]);
        
        // 设置频率
        HMI_Set_DDS_Frequency(frequencies[i]);
        HAL_Delay(200);
        
        // 查询校准表预期值
        uint16_t expected_dac = dac_calibration_get_dac_value(frequencies[i], 10);
        if (expected_dac > 0) {
            uint16_t expected_voltage = (uint16_t)((float)expected_dac * 3300.0f / 4095.0f);
            my_printf(&huart1, "校准表预期: DAC=%u, 电压=%umV\r\n", 
                     expected_dac, expected_voltage);
        } else {
            my_printf(&huart1, "校准表查询失败，跳过此频率\r\n");
            continue;
        }
        
        // 记录按键C前的DAC值
        uint16_t before_dac = HMI_Get_DAC_Voltage();
        
        // 按键C
        USART_RX_BUF[0] = 'C';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
        
        // 检查结果
        if (HMI_Get_Amplitude_Stepping_Mode()) {
            uint16_t after_dac = HMI_Get_DAC_Voltage();
            my_printf(&huart1, "✓ 成功: %umV → %umV\r\n", before_dac, after_dac);
            
            // 退出模式
            USART_RX_BUF[0] = 'D';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(300);
        } else {
            my_printf(&huart1, "✗ 失败: 无法进入步进模式\r\n");
        }
    }
    
    my_printf(&huart1, "\r\n=== 多频率按键C功能演示完成 ===\r\n");
}

/**
 * @brief 演示DAC修改的实时效果
 * @details 展示按键C修改DAC后的实时效果和后续步进调节
 * @param None
 * @retval None
 */
void demo_dac_modification_realtime_effect(void)
{
    my_printf(&huart1, "\r\n=== DAC修改实时效果演示 ===\r\n");
    
    // 设置频率
    HMI_Set_DDS_Frequency(1000);
    
    my_printf(&huart1, "1. 初始状态:\r\n");
    my_printf(&huart1, "   DAC电压: %umV\r\n", HMI_Get_DAC_Voltage());
    
    // 按键C进入模式
    my_printf(&huart1, "2. 按键C进入模式(自动设置1.0V):\r\n");
    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(1000);
    
    if (HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "   当前DAC电压: %umV\r\n", HMI_Get_DAC_Voltage());
        
        // 演示步进调节
        my_printf(&huart1, "3. 演示步进调节效果:\r\n");
        
        // 增加到1.3V
        my_printf(&huart1, "   增加到1.3V (按键8 x3):\r\n");
        for (int i = 0; i < 3; i++) {
            uint16_t before = HMI_Get_DAC_Voltage();
            USART_RX_BUF[0] = '8';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(500);
            uint16_t after = HMI_Get_DAC_Voltage();
            my_printf(&huart1, "     步进%d: %umV → %umV\r\n", i+1, before, after);
        }
        
        // 减少到1.1V
        my_printf(&huart1, "   减少到1.1V (按键9 x2):\r\n");
        for (int i = 0; i < 2; i++) {
            uint16_t before = HMI_Get_DAC_Voltage();
            USART_RX_BUF[0] = '9';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(500);
            uint16_t after = HMI_Get_DAC_Voltage();
            my_printf(&huart1, "     步进%d: %umV → %umV\r\n", i+1, before, after);
        }
        
        my_printf(&huart1, "4. 最终状态:\r\n");
        my_printf(&huart1, "   幅度: %u.%uV\r\n", 
                 HMI_Get_Stepping_Amplitude()/10, HMI_Get_Stepping_Amplitude()%10);
        my_printf(&huart1, "   DAC电压: %umV\r\n", HMI_Get_DAC_Voltage());
        
        // 退出模式
        my_printf(&huart1, "5. 退出模式(按键D):\r\n");
        USART_RX_BUF[0] = 'D';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        my_printf(&huart1, "   退出后DAC电压: %umV (保持不变)\r\n", HMI_Get_DAC_Voltage());
    }
    
    my_printf(&huart1, "\r\n=== DAC修改实时效果演示完成 ===\r\n");
}

/**
 * @brief 运行所有按键C功能演示
 * @details 运行所有相关的演示
 * @param None
 * @retval None
 */
void run_all_key_c_demos(void)
{
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "按键C查表和DAC修改功能演示\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 初始化
    my_printf(&huart1, "初始化系统...\r\n");
    dac_calibration_init();
    AD9959_Init();
    
    // 运行演示
    demo_key_c_complete_process();
    HAL_Delay(2000);
    
    demo_key_c_multiple_frequencies();
    HAL_Delay(2000);
    
    demo_dac_modification_realtime_effect();
    
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "所有按键C功能演示完成\r\n");
    my_printf(&huart1, "========================================\r\n");
}
