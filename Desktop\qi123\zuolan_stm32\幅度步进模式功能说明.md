# 幅度步进模式功能说明

## 概述

本文档描述了新增的幅度步进模式功能，该功能允许用户通过串口屏按键精确控制DAC输出幅度，基于预先校准的查找表实现高精度的幅度调节。

## 功能特性

### 核心功能
- **自动频率识别**: 进入模式时自动识别当前DDS频率
- **查找表匹配**: 在校准表中自动寻找对应频率的数据
- **DAC原始值操作**: 所有变化值和输入输出值都直接使用DAC原始值(0-4095)
- **精确幅度控制**: 基于校准表实现0.1V步进的精确幅度调节
- **初始值设置**: 自动输出1V对应的DAC原始值作为起始点
- **范围限制**: 幅度调节范围限制在1.0V-2.0V之间

### 按键功能
- **按键C**: 进入幅度步进模式
  - 自动识别当前DDS频率
  - 在校准查找表中自动寻找对应位置
  - **立即修改当前DAC输出值为1V对应的校准值**
- **按键D**: 退出幅度步进模式
- **按键8**: 在步进模式下增加0.1V幅度（自动查表并修改DAC输出）
- **按键9**: 在步进模式下减少0.1V幅度（自动查表并修改DAC输出）

## DAC原始值操作

### 核心理念
在幅度步进模式中，所有的变化值、输入输出值都直接使用DAC原始值(0-4095)进行操作，这确保了：
- **精确性**: 避免多次转换带来的精度损失
- **一致性**: 所有操作都基于相同的数值基准
- **可追踪性**: 可以直接观察DAC寄存器的实际变化

### DAC原始值范围
- **最小值**: 0 (对应0V输出)
- **最大值**: 4095 (对应3.3V参考电压)
- **分辨率**: 12位，共4096个离散值
- **步进精度**: 每个DAC原始值对应约0.8mV的电压变化

### 转换关系
```c
// DAC原始值转电压(mV)
voltage_mv = (uint16_t)((float)dac_raw_value * 3300.0f / 4095.0f);

// 电压(mV)转DAC原始值
dac_raw_value = (uint16_t)((float)voltage_mv * 4095.0f / 3300.0f);
```

## 技术实现

### 数据结构

```c
// 幅度步进模式相关变量
static uint8_t amplitude_stepping_mode = 0;   // 幅度步进模式：0=关闭，1=开启
static uint16_t current_stepping_frequency = 1000; // 当前步进模式的频率
static uint16_t current_stepping_amplitude = 10;   // 当前步进模式的幅度(mV*10)
static uint16_t current_stepping_dac_value = 0;    // 当前步进模式的DAC原始值
```

### 核心函数

#### 1. 进入幅度步进模式
```c
static void Enter_Amplitude_Stepping_Mode(void)
```
- 自动获取当前DDS频率
- 检查频率是否在校准表范围内(100Hz-3000Hz)
- 设置初始幅度为1.0V
- 从校准表获取对应的DAC值
- 设置DAC输出并更新显示

#### 2. 退出幅度步进模式
```c
static void Exit_Amplitude_Stepping_Mode(void)
```
- 退出步进模式，保持当前设置
- 恢复正常按键功能

#### 3. 幅度步进调节
```c
static void Amplitude_Step_Up(void)    // +0.1V
static void Amplitude_Step_Down(void)  // -0.1V
```
- 检查幅度范围(1.0V-2.0V)
- 从校准表获取新的DAC值
- 更新DAC输出和显示

### 公共接口函数

```c
uint8_t HMI_Get_Amplitude_Stepping_Mode(void);     // 获取模式状态
uint16_t HMI_Get_Stepping_Frequency(void);         // 获取步进频率
uint16_t HMI_Get_Stepping_Amplitude(void);         // 获取步进幅度
uint16_t HMI_Get_Stepping_DAC_Value(void);         // 获取当前DAC原始值
void HMI_Show_Amplitude_Stepping_Status(void);     // 显示状态信息
```

## 使用流程

### 1. 进入幅度步进模式
1. 确保DDS频率在支持范围内(100Hz-3000Hz)
2. 按下串口屏按键"C"
3. 系统执行以下操作：
   - 自动识别当前DDS频率
   - 在校准查找表中寻找对应的频率数据
   - 获取1.0V幅度对应的精确DAC值
   - **立即修改当前DAC输出为查找到的校准值**
   - 设置初始幅度为1.0V
4. 显示详细的进入过程信息和DAC变化

### 2. 幅度调节
1. 使用按键"8"增加0.1V幅度
   - 系统自动查找新幅度对应的校准DAC值
   - **立即修改DAC输出为新的校准值**
   - 显示详细的查表和修改过程
2. 使用按键"9"减少0.1V幅度
   - 系统自动查找新幅度对应的校准DAC值
   - **立即修改DAC输出为新的校准值**
   - 显示详细的查表和修改过程
3. 系统实时显示当前幅度、DAC原始值和转换电压
4. 幅度范围限制在1.0V-2.0V之间

### 3. 退出模式
1. 按下串口屏按键"D"
2. 系统保持当前设置并退出步进模式
3. 恢复正常按键功能

## 错误处理

### 频率不支持
- 当前DDS频率不在校准表范围内时，无法进入步进模式
- 显示错误信息和支持的频率范围

### 幅度超限
- 达到最大幅度2.0V时，无法继续增加
- 达到最小幅度1.0V时，无法继续减少
- 显示相应的限制提示

### 校准数据缺失
- 当校准表中缺少对应频率和幅度的数据时
- 显示错误信息并保持当前设置

## 校准表支持

### 支持的频率范围
- 最小频率: 100Hz
- 最大频率: 3000Hz
- 频率步进: 100Hz

### 支持的幅度范围
- 最小幅度: 1.0V (存储为10)
- 最大幅度: 2.0V (存储为20)
- 幅度步进: 0.1V (存储为1)

### 数据格式
```c
typedef struct {
    uint16_t frequency_hz;    // 频率 (Hz)
    uint16_t amplitude_mv;    // 幅度 (mV*10)
    uint16_t dac_value;       // 对应的DAC值
} dac_calibration_point_t;
```

## 与原有功能的兼容性

### 按键功能变更
- **按键8/9**: 在步进模式下执行幅度调节，在正常模式下执行原有功能
- **其他按键**: 功能保持不变

### 模式切换
- 幅度步进模式与校准模式独立运行
- 不影响原有的DDS控制和频谱分析功能

## 测试验证

### 测试文件
- `test/test_amplitude_stepping_mode.c`: 完整的功能测试
- `test/test_amplitude_stepping_mode.h`: 测试函数声明

### 测试内容
1. **基本功能测试**: 进入/退出模式、幅度调节
2. **频率兼容性测试**: 不同频率下的模式切换
3. **边界条件测试**: 幅度上下限测试

### 运行测试
```c
run_all_amplitude_stepping_tests();
```

## 注意事项

1. **频率限制**: 只有在校准表支持的频率范围内才能使用步进模式
2. **幅度范围**: 步进调节限制在1.0V-2.0V范围内
3. **数据精度**: 基于实际测量的校准数据，确保输出精度
4. **模式状态**: 退出步进模式时保持当前设置不变

## 更新记录

- **2025-08-02**: 初始版本，实现基本的幅度步进功能
- 新增按键C/D控制进入/退出模式
- 新增基于校准表的精确幅度调节
- 新增完整的错误处理和状态显示
