Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.ADC_IRQHandler) for ADC_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to fmc.o(i.MX_FMC_Init) for MX_FMC_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to dac.o(i.MX_DAC_Init) for MX_DAC_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to cmd_to_fun.o(i.CTRL_INIT) for CTRL_INIT
    main.o(i.main) refers to da_output.o(i.DA_Init) for DA_Init
    main.o(i.main) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    main.o(i.main) refers to app_pid.o(i.PID_Init) for PID_Init
    main.o(i.main) refers to my_fft.o(i.fft_init) for fft_init
    main.o(i.main) refers to ad9959.o(i.AD9959_Init) for AD9959_Init
    main.o(i.main) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.main) refers to dac_app.o(i.DAC_Init) for DAC_Init
    main.o(i.main) refers to scheduler.o(i.scheduler_init) for scheduler_init
    main.o(i.main) refers to scheduler.o(i.scheduler_run) for scheduler_run
    main.o(i.main) refers to my_usart.o(.data) for rxTemp1
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dac.o(i.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(i.HAL_DAC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    dac.o(i.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(i.MX_DAC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dac.o(i.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(i.MX_DAC_Init) refers to dac.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    fmc.o(i.HAL_FMC_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.HAL_FMC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(i.HAL_FMC_MspInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(i.HAL_SRAM_MspDeInit) refers to fmc.o(.data) for .data
    fmc.o(i.HAL_SRAM_MspInit) refers to fmc.o(i.HAL_FMC_MspInit) for HAL_FMC_MspInit
    fmc.o(i.MX_FMC_Init) refers to memseta.o(.text) for __aeabi_memclr4
    fmc.o(i.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(i.MX_FMC_Init) refers to main.o(i.Error_Handler) for Error_Handler
    fmc.o(i.MX_FMC_Init) refers to fmc.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) for HAL_ADC_IRQHandler
    stm32f4xx_it.o(i.ADC_IRQHandler) refers to adc.o(.bss) for hadc1
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.NMI_Handler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    ad_measure.o(i.ad_proc) refers to ad_measure.o(i.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(i.readFIFOData) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.readFIFOData) refers to cmd_to_fun.o(i.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(i.setSamplingFrequency) refers to cmd_to_fun.o(i.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(i.setSamplingFrequency) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.setSamplingFrequency) for setSamplingFrequency
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(i.vpp_adc_parallel) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad_measure.o(i.vpp_adc_parallel) refers to cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.readFIFOData) for readFIFOData
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(i.findMinMax) for findMinMax
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.data) for .data
    ad_measure.o(i.vpp_adc_parallel) refers to ad_measure.o(.bss) for .bss
    ad9959.o(i.AD9959_CS_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_CS_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Ch) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_IO_UpDate) refers to ad9959.o(i.AD9959_UP_L) for AD9959_UP_L
    ad9959.o(i.AD9959_IO_UpDate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_IO_UpDate) refers to ad9959.o(i.AD9959_UP_H) for AD9959_UP_H
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_PDC_L) for AD9959_PDC_L
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_Start) for AD9959_Start
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_Reset) for AD9959_Reset
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_P0_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P0_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P1_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P1_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P2_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P2_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P3_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P3_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_PDC_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_PDC_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_RST_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_RST_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Reset) refers to ad9959.o(i.AD9959_RST_L) for AD9959_RST_L
    ad9959.o(i.AD9959_Reset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_Reset) refers to ad9959.o(i.AD9959_RST_H) for AD9959_RST_H
    ad9959.o(i.AD9959_SCK_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SCK_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO0_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO0_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO1_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO1_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO2_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO2_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO3_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO3_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Fre) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959_Set_Fre) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959_Set_Fre) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Pha) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Pha) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Fre) for AD9959_Set_Fre
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Pha) for AD9959_Set_Pha
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Amp) for AD9959_Set_Amp
    ad9959.o(i.AD9959_Start) refers to ad9959.o(i.AD9959_CS_H) for AD9959_CS_H
    ad9959.o(i.AD9959_Start) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_Start) refers to ad9959.o(i.AD9959_CS_L) for AD9959_CS_L
    ad9959.o(i.AD9959_Sweep_Phase) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959_Sweep_Phase) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959_Sweep_Phase) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Sweep_Phase) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Sweep_Phase) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_UP_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_UP_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SDIO0_H) for AD9959_SDIO0_H
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SDIO0_L) for AD9959_SDIO0_L
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SCK_H) for AD9959_SCK_H
    ad9959.o(i.AD9959_WByte) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SCK_L) for AD9959_SCK_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_SDIO3_L) for AD9959_SDIO3_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_CS_L) for AD9959_CS_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_WByte) for AD9959_WByte
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_CS_H) for AD9959_CS_H
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_SDIO3_H) for AD9959_SDIO3_H
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(.data) for .data
    ad9959.o(i.AD9959__Sweep_Amp) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959__Sweep_Amp) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959__Sweep_Fre) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959__Sweep_Fre) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959__Sweep_Fre) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P3_L) for AD9959_P3_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P3_H) for AD9959_P3_H
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P0_L) for AD9959_P0_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P0_H) for AD9959_P0_H
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P1_L) for AD9959_P1_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P1_H) for AD9959_P1_H
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P2_L) for AD9959_P2_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P2_H) for AD9959_P2_H
    ad9959.o(i.AD9959_proc) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.AD9959_proc) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_proc) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    ad9959.o(i.AD9959_proc) refers to ad9959.o(.data) for .data
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(i.DA_Apply_Settings) refers to ffixul.o(.text) for __aeabi_f2ulz
    da_output.o(i.DA_Apply_Settings) refers to uldiv.o(.text) for __aeabi_uldivmod
    da_output.o(i.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(i.DA_Apply_Settings) refers to cmd_to_fun.o(i.DA_FPGA_START) for DA_FPGA_START
    da_output.o(i.DA_Apply_Settings) refers to da_output.o(.bss) for .bss
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    da_output.o(i.DA_Init) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.DA_SetConfig) refers to da_output.o(.bss) for .bss
    da_output.o(i.wave_test) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    da_output.o(i.wave_test) refers to stm32f4xx_hal.o(.data) for uwTick
    da_output.o(i.wave_test) refers to da_output.o(.data) for .data
    da_output.o(i.wave_test) refers to da_output.o(.bss) for .bss
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(i.fre_measure) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(i.fre_measure) refers to cmd_to_fun.o(i.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad1) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(i.fre_measure) for fre_measure
    freq_measure.o(i.fre_measure_ad2) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad1) for fre_measure_ad1
    freq_measure.o(i.freq_proc) refers to freq_measure.o(i.fre_measure_ad2) for fre_measure_ad2
    freq_measure.o(i.freq_proc) refers to f2d.o(.text) for __aeabi_f2d
    freq_measure.o(i.freq_proc) refers to my_usart.o(i.my_printf) for my_printf
    freq_measure.o(i.freq_proc) refers to freq_measure.o(.data) for .data
    freq_measure.o(i.freq_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.get_current_ad_frequency) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to key_app.o(i.key_read) for key_read
    key_app.o(i.key_proc) refers to my_usart.o(i.my_printf) for my_printf
    key_app.o(i.key_proc) refers to key_app.o(.data) for .data
    key_app.o(i.key_proc) refers to usart.o(.bss) for huart1
    key_app.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(i.set_current_ad_frequency) refers to key_app.o(.data) for .data
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc.o(.bss) for hadc1
    adc_app.o(i.HAL_ADC_ConvCpltCallback) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to my_usart.o(i.my_printf) for my_printf
    adc_app.o(i.adc_task) refers to memseta.o(.text) for __aeabi_memclr4
    adc_app.o(i.adc_task) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_task) refers to adc_app.o(.data) for .data
    adc_app.o(i.adc_task) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_task) refers to usart.o(.bss) for huart1
    adc_app.o(i.adc_task) refers to adc.o(.bss) for hadc1
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    adc_app.o(i.adc_tim_dma_init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc_app.o(i.adc_tim_dma_init) refers to tim.o(.bss) for htim3
    adc_app.o(i.adc_tim_dma_init) refers to adc_app.o(.bss) for .bss
    adc_app.o(i.adc_tim_dma_init) refers to adc.o(.bss) for hadc1
    dac_app.o(i.DAC_GetVoltage) refers to dac_app.o(.data) for .data
    dac_app.o(i.DAC_Init) refers to dac_app.o(i.DAC_SetVoltage) for DAC_SetVoltage
    dac_app.o(i.DAC_Init) refers to my_usart.o(i.my_printf) for my_printf
    dac_app.o(i.DAC_Init) refers to dac_app.o(.data) for .data
    dac_app.o(i.DAC_Init) refers to usart.o(.bss) for huart1
    dac_app.o(i.DAC_SetVoltage) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_Start) for HAL_DAC_Start
    dac_app.o(i.DAC_SetVoltage) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue) for HAL_DAC_SetValue
    dac_app.o(i.DAC_SetVoltage) refers to dac.o(.bss) for hdac
    dac_app.o(i.DAC_SetVoltage) refers to dac_app.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_get_amplitude_range) refers to dac_amplitude_calibration.o(i.dac_calibration_init) for dac_calibration_init
    dac_amplitude_calibration.o(i.dac_calibration_get_amplitude_range) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_get_amplitude_range) refers to dac_amplitude_calibration.o(.constdata) for .constdata
    dac_amplitude_calibration.o(i.dac_calibration_get_closest_point) refers to dac_amplitude_calibration.o(i.dac_calibration_init) for dac_calibration_init
    dac_amplitude_calibration.o(i.dac_calibration_get_closest_point) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_get_closest_point) refers to dac_amplitude_calibration.o(.constdata) for .constdata
    dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) refers to dac_amplitude_calibration.o(i.dac_calibration_init) for dac_calibration_init
    dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) refers to dac_amplitude_calibration.o(i.dac_calibration_interpolate_dac_value) for dac_calibration_interpolate_dac_value
    dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) refers to dac_amplitude_calibration.o(.constdata) for .constdata
    dac_amplitude_calibration.o(i.dac_calibration_get_supported_frequencies) refers to dac_amplitude_calibration.o(i.dac_calibration_init) for dac_calibration_init
    dac_amplitude_calibration.o(i.dac_calibration_get_supported_frequencies) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_get_supported_frequencies) refers to dac_amplitude_calibration.o(.bss) for .bss
    dac_amplitude_calibration.o(i.dac_calibration_get_supported_frequencies) refers to dac_amplitude_calibration.o(.constdata) for .constdata
    dac_amplitude_calibration.o(i.dac_calibration_init) refers to my_usart.o(i.my_printf) for my_printf
    dac_amplitude_calibration.o(i.dac_calibration_init) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_init) refers to usart.o(.bss) for huart1
    dac_amplitude_calibration.o(i.dac_calibration_interpolate_dac_value) refers to dac_amplitude_calibration.o(.constdata) for .constdata
    dac_amplitude_calibration.o(i.dac_calibration_interpolate_dac_value) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_is_supported) refers to dac_amplitude_calibration.o(i.dac_calibration_init) for dac_calibration_init
    dac_amplitude_calibration.o(i.dac_calibration_is_supported) refers to dac_amplitude_calibration.o(.data) for .data
    dac_amplitude_calibration.o(i.dac_calibration_is_supported) refers to dac_amplitude_calibration.o(.constdata) for .constdata
    dac_amplitude_calibration.o(.data) refers to dac_amplitude_calibration.o(.constdata) for calibration_data
    my_fft.o(i.calculate_fft_spectrum) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(i.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(i.calculate_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_precise_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.calculate_thd) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    my_fft.o(i.calculate_thd) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.configure_da_output_from_peaks) refers to da_output.o(i.DA_SetConfig) for DA_SetConfig
    my_fft.o(i.configure_da_output_from_peaks) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.configure_da_output_from_peaks) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.configure_da_output_from_peaks) refers to da_output.o(i.DA_Apply_Settings) for DA_Apply_Settings
    my_fft.o(i.configure_da_output_from_peaks) refers to my_fft.o(.data) for .data
    my_fft.o(i.configure_da_output_from_peaks) refers to usart.o(.bss) for huart1
    my_fft.o(i.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(i.fft_init) refers to my_fft.o(i.generate_hanning_window) for generate_hanning_window
    my_fft.o(i.fft_init) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.find_dual_peaks) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(i.find_dual_peaks) refers to my_fft.o(i.find_spectrum_peaks) for find_spectrum_peaks
    my_fft.o(i.find_dual_peaks) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_fft.o(i.find_spectrum_peaks) refers to my_fft.o(i.calculate_precise_frequency) for calculate_precise_frequency
    my_fft.o(i.find_spectrum_peaks) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(i.generate_hanning_window) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.get_peak1_frequency) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_peak1_magnitude) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_peak2_frequency) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_peak2_magnitude) refers to my_fft.o(.data) for .data
    my_fft.o(i.get_precise_peak_frequency) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_dual_peaks_info) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_dual_peaks_info) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.output_dual_peaks_info) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_dual_peaks_info) refers to my_fft.o(.data) for .data
    my_fft.o(i.output_dual_peaks_info) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.output_dual_peaks_info) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_usart.o(i.my_printf) for my_printf
    my_fft.o(i.output_fft_spectrum) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.get_precise_peak_frequency) for get_precise_peak_frequency
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.round_to_nearest_k) for round_to_nearest_k
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.calculate_thd) for calculate_thd
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.find_dual_peaks) for find_dual_peaks
    my_fft.o(i.output_fft_spectrum) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(i.output_dual_peaks_info) for output_dual_peaks_info
    my_fft.o(i.output_fft_spectrum) refers to usart.o(.bss) for huart1
    my_fft.o(i.output_fft_spectrum) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.perform_dual_peak_analysis) refers to key_app.o(i.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(i.calculate_fft_spectrum) for calculate_fft_spectrum
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(i.find_dual_peaks) for find_dual_peaks
    my_fft.o(i.perform_dual_peak_analysis) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(i.output_dual_peaks_info) for output_dual_peaks_info
    my_fft.o(i.perform_dual_peak_analysis) refers to my_fft.o(.bss) for .bss
    my_fft.o(i.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_filter.o(i.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(i.arm_fir_f32_lp) refers to my_filter.o(.constdata) for .constdata
    phase_measure.o(i.calculate_phase_diff) refers to phase_measure.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman) refers to kalman.o(.data) for .data
    kalman.o(i.kalman) refers to kalman.o(.bss) for .bss
    kalman.o(i.kalman_thd) refers to kalman.o(i.Kalman_init) for Kalman_init
    kalman.o(i.kalman_thd) refers to kalman.o(i.kalman_filter) for kalman_filter
    kalman.o(i.kalman_thd) refers to kalman.o(.data) for .data
    kalman.o(i.kalman_thd) refers to kalman.o(.bss) for .bss
    my_hmi.o(i.HMI_Send_Float) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Float) refers to dflti.o(.text) for __aeabi_i2d
    my_hmi.o(i.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(i.HMI_Send_Float) refers to f2d.o(.text) for __aeabi_f2d
    my_hmi.o(i.HMI_Send_Float) refers to dmul.o(.text) for __aeabi_dmul
    my_hmi.o(i.HMI_Send_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    my_hmi.o(i.HMI_Send_Float) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Int) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_Int) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_String) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Send_String) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Wave_Clear) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Wave_Clear) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hmi.o(i.HMI_Write_Wave_Low) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(i.HMI_Write_Wave_Low) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to memseta.o(.text) for __aeabi_memclr
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart_pack.o(i.ParseFrame) for ParseFrame
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    my_usart.o(i.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.ParseFrame) refers to my_usart_pack.o(i.ParseDataToVariables) for ParseDataToVariables
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.SendFrame) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.data) for .data
    hmi_key_handler.o(i.Analyze_Filter_Response) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.Analyze_Filter_Response) refers to f2d.o(.text) for __aeabi_f2d
    hmi_key_handler.o(i.Analyze_Filter_Response) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    hmi_key_handler.o(i.Analyze_Filter_Response) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.DDS_Disable_Output) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.DDS_Disable_Output) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.DDS_Disable_Output) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.DDS_Enable_Output) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.DDS_Enable_Output) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.DDS_Enable_Output) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.DDS_Update_Display) refers to f2d.o(.text) for __aeabi_f2d
    hmi_key_handler.o(i.DDS_Update_Display) refers to printfa.o(i.__0sprintf) for __2sprintf
    hmi_key_handler.o(i.DDS_Update_Display) refers to memcpya.o(.text) for __aeabi_memcpy4
    hmi_key_handler.o(i.DDS_Update_Display) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    hmi_key_handler.o(i.DDS_Update_Display) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.DDS_Update_Display) refers to usart.o(.bss) for huart2
    hmi_key_handler.o(i.DDS_Update_Display) refers to hmi_key_handler.o(.bss) for .bss
    hmi_key_handler.o(i.HMI_Clear_Key_Status) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to memcpya.o(.text) for __aeabi_memcpy4
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to usart.o(.bss) for huart2
    hmi_key_handler.o(i.HMI_DDS_Display_Init) refers to hmi_key_handler.o(.conststring) for .conststring
    hmi_key_handler.o(i.HMI_DDS_Display_Update) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Disable_Calibration_Mode) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Disable_Calibration_Mode) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Disable_Calibration_Mode) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to dac_amplitude_calibration.o(i.dac_calibration_init) for dac_calibration_init
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to dac_amplitude_calibration.o(i.dac_calibration_is_supported) for dac_calibration_is_supported
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) for dac_calibration_get_dac_value
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to dac_app.o(i.DAC_SetVoltage) for DAC_SetVoltage
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Enable_Calibration_Mode) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.HMI_Get_Calibrated_Amplitude) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_Calibration_Mode) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DAC_Voltage) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DDS_Frequency) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DDS_Status) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_DDS_Waveform) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Get_Key_Status) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_Get_Key_Status) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.Process_Parameter_Command) for Process_Parameter_Command
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.Process_Amplitude_Command) for Process_Amplitude_Command
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.DDS_Enable_Output) for DDS_Enable_Output
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.DDS_Disable_Output) for DDS_Disable_Output
    hmi_key_handler.o(i.HMI_Key_Process) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Key_Process) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.Start_Spectrum_Analysis) for Start_Spectrum_Analysis
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.Set_Frequency_1kHz) for Set_Frequency_1kHz
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.Voltage_Step_Up) for Voltage_Step_Up
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(i.Voltage_Step_Down) for Voltage_Step_Down
    hmi_key_handler.o(i.HMI_Key_Process) refers to my_usart.o(.data) for USART_RX_STA
    hmi_key_handler.o(i.HMI_Key_Process) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.HMI_Key_Process) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Key_Process) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to dac_amplitude_calibration.o(i.dac_calibration_is_supported) for dac_calibration_is_supported
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) for dac_calibration_get_dac_value
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to dac_app.o(i.DAC_SetVoltage) for DAC_SetVoltage
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to hmi_key_handler.o(.conststring) for .conststring
    hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.HMI_Set_DAC_Voltage) refers to dac_app.o(i.DAC_SetVoltage) for DAC_SetVoltage
    hmi_key_handler.o(i.HMI_Set_DAC_Voltage) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_DAC_Voltage) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Set_DDS_Frequency) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Frequency) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_DDS_Frequency) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Set_DDS_Output) refers to hmi_key_handler.o(i.DDS_Enable_Output) for DDS_Enable_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Output) refers to hmi_key_handler.o(i.DDS_Disable_Output) for DDS_Disable_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Waveform) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.HMI_Set_DDS_Waveform) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.HMI_Set_DDS_Waveform) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Show_Calibration_Status) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.HMI_Show_Calibration_Status) refers to dac_amplitude_calibration.o(i.dac_calibration_is_supported) for dac_calibration_is_supported
    hmi_key_handler.o(i.HMI_Show_Calibration_Status) refers to dac_amplitude_calibration.o(i.dac_calibration_get_amplitude_range) for dac_calibration_get_amplitude_range
    hmi_key_handler.o(i.HMI_Show_Calibration_Status) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.HMI_Show_Calibration_Status) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to memseta.o(.text) for __aeabi_memclr4
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to atoi.o(.text) for atoi
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to hmi_key_handler.o(i.HMI_Set_DAC_Voltage) for HMI_Set_DAC_Voltage
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.Process_Amplitude_Command) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.Process_Parameter_Command) refers to memseta.o(.text) for __aeabi_memclr4
    hmi_key_handler.o(i.Process_Parameter_Command) refers to atoi.o(.text) for atoi
    hmi_key_handler.o(i.Process_Parameter_Command) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.Process_Parameter_Command) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.Process_Parameter_Command) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.Process_Parameter_Command) refers to my_usart.o(.bss) for USART_RX_BUF
    hmi_key_handler.o(i.Set_Frequency_1kHz) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    hmi_key_handler.o(i.Set_Frequency_1kHz) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.Set_Frequency_1kHz) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.Set_Frequency_1kHz) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.Set_Frequency_1kHz) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to my_fft.o(i.fft_init) for fft_init
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to hmi_key_handler.o(i.DDS_Enable_Output) for DDS_Enable_Output
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to f2d.o(.text) for __aeabi_f2d
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to hmi_key_handler.o(i.HMI_Set_DDS_Frequency) for HMI_Set_DDS_Frequency
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to ad_measure.o(i.vpp_adc_parallel) for vpp_adc_parallel
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to my_fft.o(i.calculate_fft_spectrum) for calculate_fft_spectrum
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to hmi_key_handler.o(i.Analyze_Filter_Response) for Analyze_Filter_Response
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to hmi_key_handler.o(i.DDS_Disable_Output) for DDS_Disable_Output
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to hmi_key_handler.o(.bss) for .bss
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to my_fft.o(.bss) for fft_magnitude
    hmi_key_handler.o(i.Start_Spectrum_Analysis) refers to ad_measure.o(.bss) for fifo_data1_f
    hmi_key_handler.o(i.Voltage_Step_Down) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.Voltage_Step_Down) refers to dac_app.o(i.DAC_SetVoltage) for DAC_SetVoltage
    hmi_key_handler.o(i.Voltage_Step_Down) refers to f2d.o(.text) for __aeabi_f2d
    hmi_key_handler.o(i.Voltage_Step_Down) refers to dac_amplitude_calibration.o(i.dac_calibration_is_supported) for dac_calibration_is_supported
    hmi_key_handler.o(i.Voltage_Step_Down) refers to dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) for dac_calibration_get_dac_value
    hmi_key_handler.o(i.Voltage_Step_Down) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.Voltage_Step_Down) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.Voltage_Step_Down) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.Voltage_Step_Down) refers to hmi_key_handler.o(.conststring) for .conststring
    hmi_key_handler.o(i.Voltage_Step_Up) refers to my_usart.o(i.my_printf) for my_printf
    hmi_key_handler.o(i.Voltage_Step_Up) refers to dac_amplitude_calibration.o(i.dac_calibration_is_supported) for dac_calibration_is_supported
    hmi_key_handler.o(i.Voltage_Step_Up) refers to dac_amplitude_calibration.o(i.dac_calibration_get_dac_value) for dac_calibration_get_dac_value
    hmi_key_handler.o(i.Voltage_Step_Up) refers to dac_app.o(i.DAC_SetVoltage) for DAC_SetVoltage
    hmi_key_handler.o(i.Voltage_Step_Up) refers to f2d.o(.text) for __aeabi_f2d
    hmi_key_handler.o(i.Voltage_Step_Up) refers to hmi_key_handler.o(i.DDS_Update_Display) for DDS_Update_Display
    hmi_key_handler.o(i.Voltage_Step_Up) refers to hmi_key_handler.o(.data) for .data
    hmi_key_handler.o(i.Voltage_Step_Up) refers to usart.o(.bss) for huart1
    hmi_key_handler.o(i.Voltage_Step_Up) refers to hmi_key_handler.o(.conststring) for .conststring
    hmi_key_handler.o(.data) refers to hmi_key_handler.o(.conststring) for .conststring
    scheduler.o(i.scheduler_init) refers to scheduler.o(.data) for .data
    scheduler.o(i.scheduler_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.scheduler_run) refers to scheduler.o(.data) for .data
    scheduler.o(i.uart_proc) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(i.uart_proc) refers to my_usart.o(i.my_printf) for my_printf
    scheduler.o(i.uart_proc) refers to ad_measure.o(.data) for vol_amp2
    scheduler.o(i.uart_proc) refers to app_pid.o(.data) for output
    scheduler.o(i.uart_proc) refers to usart.o(.bss) for huart1
    scheduler.o(.data) refers to key_app.o(i.key_proc) for key_proc
    scheduler.o(.data) refers to hmi_key_handler.o(i.HMI_Key_Process) for HMI_Key_Process
    scheduler.o(.data) refers to hmi_key_handler.o(i.HMI_DDS_Display_Update) for HMI_DDS_Display_Update
    app_pid.o(i.PID_Init) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to app_pid.o(i.increment_pid_ctrl) for increment_pid_ctrl
    app_pid.o(i.Pid_Proc) refers to ad_measure.o(.data) for vol_amp2
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.data) for .data
    app_pid.o(i.Pid_Proc) refers to app_pid.o(.bss) for .bss
    app_pid.o(i.Pid_Proc) refers to ad9959.o(.data) for pid_vin
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to adc_app.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP) for FLASH_OB_DisablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig) for FLASH_OB_BootConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP) for FLASH_OB_EnablePCROP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit) refers to dac.o(i.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Init) refers to dac.o(i.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to fmc.o(i.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to fmc.o(i.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) for SRAM_DMACpltProt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMACplt) for SRAM_DMACplt
    stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(i.SRAM_DMAError) for SRAM_DMAError
    stm32f4xx_hal_sram.o(i.SRAM_DMACplt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(i.SRAM_DMAError) refers to stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    log10f.o(i.__hardfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to errno.o(i.__set_errno) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____hardfp_log10f$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f_x.o(i.____hardfp_log10f$lsc) refers to errno.o(i.__set_errno) for __set_errno
    log10f_x.o(i.____hardfp_log10f$lsc) refers to log10f_x.o(.constdata) for .constdata
    log10f_x.o(i.____softfp_log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.____softfp_log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(i.__log10f$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f_x.o(i.__log10f$lsc) refers to log10f_x.o(i.____hardfp_log10f$lsc) for ____hardfp_log10f$lsc
    log10f_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atoi.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtol.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    ctype_o.o(.text) refers to ctype_o.o(.constdata) for .constdata
    ctype_o.o(.constdata) refers to ctype_o.o(.constdata) for __ctype_table
    isalnum_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isalpha_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isblank_o.o(.text) refers to ctype_o.o(.constdata) for __ctype_table
    iscntrl_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isgraph_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    islower_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isprint_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    ispunct_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isspace_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isupper_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    isxdigit_o.o(.text) refers to ctype_o.o(.text) for __rt_ctype_table
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to errno.o(i.__aeabi_errno_addr) for __aeabi_errno_addr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429xx.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (60 bytes).
    Removing dac.o(.rev16_text), (4 bytes).
    Removing dac.o(.revsh_text), (4 bytes).
    Removing dac.o(.rrx_text), (6 bytes).
    Removing dac.o(i.HAL_DAC_MspDeInit), (40 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing fmc.o(.rev16_text), (4 bytes).
    Removing fmc.o(.revsh_text), (4 bytes).
    Removing fmc.o(.rrx_text), (6 bytes).
    Removing fmc.o(i.HAL_SRAM_MspDeInit), (92 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (124 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(.rev16_text), (4 bytes).
    Removing ad_measure.o(.revsh_text), (4 bytes).
    Removing ad_measure.o(.rrx_text), (6 bytes).
    Removing ad_measure.o(i.ad_proc), (24 bytes).
    Removing ad9959.o(.rev16_text), (4 bytes).
    Removing ad9959.o(.revsh_text), (4 bytes).
    Removing ad9959.o(.rrx_text), (6 bytes).
    Removing ad9959.o(i.AD9959_P0_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P0_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P1_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P1_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P2_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P2_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P3_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P3_L), (16 bytes).
    Removing ad9959.o(i.AD9959_PDC_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO1_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO1_L), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO2_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO2_L), (16 bytes).
    Removing ad9959.o(i.AD9959_Sweep_Phase), (220 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Amp), (148 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Fre), (260 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Trigger), (90 bytes).
    Removing ad9959.o(i.AD9959_proc), (84 bytes).
    Removing ad9959.o(.data), (44 bytes).
    Removing ad9959.o(.data), (1 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing da_output.o(.rev16_text), (4 bytes).
    Removing da_output.o(.revsh_text), (4 bytes).
    Removing da_output.o(.rrx_text), (6 bytes).
    Removing da_output.o(i.wave_test), (76 bytes).
    Removing da_output.o(.data), (8 bytes).
    Removing freq_measure.o(.rev16_text), (4 bytes).
    Removing freq_measure.o(.revsh_text), (4 bytes).
    Removing freq_measure.o(.rrx_text), (6 bytes).
    Removing freq_measure.o(i.fre_measure), (192 bytes).
    Removing freq_measure.o(i.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(i.fre_measure_ad2), (20 bytes).
    Removing freq_measure.o(i.freq_proc), (60 bytes).
    Removing freq_measure.o(.data), (24 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(i.get_current_ad_frequency), (12 bytes).
    Removing key_app.o(i.set_current_ad_frequency), (12 bytes).
    Removing key_app.o(.data), (4 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rrx_text), (6 bytes).
    Removing adc_app.o(i.adc_task), (152 bytes).
    Removing adc_app.o(i.adc_tim_dma_init), (52 bytes).
    Removing adc_app.o(.bss), (12288 bytes).
    Removing adc_app.o(.bss), (4096 bytes).
    Removing adc_app.o(.data), (4 bytes).
    Removing dac_app.o(.rev16_text), (4 bytes).
    Removing dac_app.o(.revsh_text), (4 bytes).
    Removing dac_app.o(.rrx_text), (6 bytes).
    Removing dac_app.o(i.DAC_GetVoltage), (12 bytes).
    Removing dac_amplitude_calibration.o(.rev16_text), (4 bytes).
    Removing dac_amplitude_calibration.o(.revsh_text), (4 bytes).
    Removing dac_amplitude_calibration.o(.rrx_text), (6 bytes).
    Removing dac_amplitude_calibration.o(i.dac_calibration_get_amplitude_range), (124 bytes).
    Removing dac_amplitude_calibration.o(i.dac_calibration_get_closest_point), (144 bytes).
    Removing dac_amplitude_calibration.o(i.dac_calibration_get_supported_frequencies), (132 bytes).
    Removing dac_amplitude_calibration.o(.bss), (100 bytes).
    Removing my_fft.o(.rev16_text), (4 bytes).
    Removing my_fft.o(.revsh_text), (4 bytes).
    Removing my_fft.o(.rrx_text), (6 bytes).
    Removing my_fft.o(i.calculate_precise_frequency), (160 bytes).
    Removing my_fft.o(i.calculate_thd), (248 bytes).
    Removing my_fft.o(i.configure_da_output_from_peaks), (360 bytes).
    Removing my_fft.o(i.find_dual_peaks), (158 bytes).
    Removing my_fft.o(i.find_spectrum_peaks), (336 bytes).
    Removing my_fft.o(i.get_peak1_frequency), (12 bytes).
    Removing my_fft.o(i.get_peak1_magnitude), (12 bytes).
    Removing my_fft.o(i.get_peak2_frequency), (12 bytes).
    Removing my_fft.o(i.get_peak2_magnitude), (12 bytes).
    Removing my_fft.o(i.get_precise_peak_frequency), (208 bytes).
    Removing my_fft.o(i.output_dual_peaks_info), (324 bytes).
    Removing my_fft.o(i.output_fft_spectrum), (704 bytes).
    Removing my_fft.o(i.perform_dual_peak_analysis), (76 bytes).
    Removing my_fft.o(i.round_to_nearest_k), (36 bytes).
    Removing my_fft.o(.data), (16 bytes).
    Removing my_filter.o(.rev16_text), (4 bytes).
    Removing my_filter.o(.revsh_text), (4 bytes).
    Removing my_filter.o(.rrx_text), (6 bytes).
    Removing my_filter.o(i.arm_fir_f32_lp), (40 bytes).
    Removing my_filter.o(.constdata), (4 bytes).
    Removing my_filter.o(.constdata), (204 bytes).
    Removing phase_measure.o(.rev16_text), (4 bytes).
    Removing phase_measure.o(.revsh_text), (4 bytes).
    Removing phase_measure.o(.rrx_text), (6 bytes).
    Removing phase_measure.o(i.calculate_phase_diff), (208 bytes).
    Removing phase_measure.o(.data), (4 bytes).
    Removing kalman.o(.rev16_text), (4 bytes).
    Removing kalman.o(.revsh_text), (4 bytes).
    Removing kalman.o(.rrx_text), (6 bytes).
    Removing kalman.o(i.Kalman_init), (48 bytes).
    Removing kalman.o(i.kalman), (96 bytes).
    Removing kalman.o(i.kalman_filter), (82 bytes).
    Removing kalman.o(i.kalman_thd), (64 bytes).
    Removing kalman.o(.bss), (308 bytes).
    Removing kalman.o(.data), (4 bytes).
    Removing my_hmi.o(.rev16_text), (4 bytes).
    Removing my_hmi.o(.revsh_text), (4 bytes).
    Removing my_hmi.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(i.HMI_Send_Float), (192 bytes).
    Removing my_hmi.o(i.HMI_Send_Int), (124 bytes).
    Removing my_hmi.o(i.HMI_Wave_Clear), (124 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Fast), (184 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Low), (132 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(.data), (4 bytes).
    Removing my_usart.o(.data), (4 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart_pack.o(.rev16_text), (4 bytes).
    Removing my_usart_pack.o(.revsh_text), (4 bytes).
    Removing my_usart_pack.o(.rrx_text), (6 bytes).
    Removing my_usart_pack.o(i.PrepareFrame), (232 bytes).
    Removing my_usart_pack.o(i.SendFrame), (24 bytes).
    Removing my_usart_pack.o(i.SetParseTemplate), (52 bytes).
    Removing hmi_key_handler.o(.rev16_text), (4 bytes).
    Removing hmi_key_handler.o(.revsh_text), (4 bytes).
    Removing hmi_key_handler.o(.rrx_text), (6 bytes).
    Removing hmi_key_handler.o(i.HMI_Clear_Key_Status), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_DDS_Display_Init), (196 bytes).
    Removing hmi_key_handler.o(i.HMI_Disable_Calibration_Mode), (84 bytes).
    Removing hmi_key_handler.o(i.HMI_Enable_Calibration_Mode), (328 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_Calibrated_Amplitude), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_Calibration_Mode), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DAC_Voltage), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Frequency), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Status), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_DDS_Waveform), (12 bytes).
    Removing hmi_key_handler.o(i.HMI_Get_Key_Status), (28 bytes).
    Removing hmi_key_handler.o(i.HMI_Set_Calibrated_Amplitude), (320 bytes).
    Removing hmi_key_handler.o(i.HMI_Set_DDS_Output), (10 bytes).
    Removing hmi_key_handler.o(i.HMI_Set_DDS_Waveform), (44 bytes).
    Removing hmi_key_handler.o(i.HMI_Show_Calibration_Status), (540 bytes).
    Removing cmd_to_fun.o(.rev16_text), (4 bytes).
    Removing cmd_to_fun.o(.revsh_text), (4 bytes).
    Removing cmd_to_fun.o(.rrx_text), (6 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_DISABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_CLR_ENABLE), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_START), (30 bytes).
    Removing cmd_to_fun.o(i.AD_FREQ_STOP), (30 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler.o(i.uart_proc), (76 bytes).
    Removing scheduler.o(.data), (4 bytes).
    Removing app_pid.o(.rev16_text), (4 bytes).
    Removing app_pid.o(.revsh_text), (4 bytes).
    Removing app_pid.o(.rrx_text), (6 bytes).
    Removing app_pid.o(i.Pid_Proc), (80 bytes).
    Removing app_pid.o(i.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.data), (8 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt), (116 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAError), (22 bytes).
    Removing stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt), (10 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (64 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (198 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (136 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (296 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA), (396 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (312 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (62 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (524 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (64 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (166 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (268 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (276 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (100 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (92 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (300 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (100 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (60 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (220 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLSAI), (60 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLSAI), (132 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (684 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (448 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (52 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (136 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (208 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (96 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (68 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (24 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BootConfig), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnablePCROP), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (116 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (28 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (52 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBGetConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_AdvOBProgram), (62 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (96 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OB_SelectPCROP), (24 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (376 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (42 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (34 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (98 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2980 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (106 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (310 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (88 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (74 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (148 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (204 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableOverDrive), (124 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnterUnderDriveSTOPMode), (100 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (180 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (40 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (16 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (232 bytes).
    Removing stm32f4xx_hal_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32f4xx_hal_dac.o(i.DAC_DMAHalfConvCpltCh1), (10 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_DeInit), (32 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_IRQHandler), (112 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Start_DMA), (264 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop), (34 bytes).
    Removing stm32f4xx_hal_dac.o(i.HAL_DAC_Stop_DMA), (92 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.DAC_DMAHalfConvCpltCh2), (10 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualSetValue), (32 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStart), (120 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_DualStop), (36 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_NoiseWaveGenerate), (78 bytes).
    Removing stm32f4xx_hal_dac_ex.o(i.HAL_DACEx_TriangleWaveGenerate), (78 bytes).
    Removing stm32f4xx_ll_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ll_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_AttributeSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_CommonSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_DeInit), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Disable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_ECC_Enable), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_GetECC), (110 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NAND_Init), (76 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_DeInit), (52 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_WriteOperation_Enable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_AttributeSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_CommonSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_IOSpace_Timing_Init), (26 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_PCCARD_Init), (36 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_GetModeStatus), (22 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Init), (116 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_ProgramRefreshRate), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SendCommand), (84 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_SetAutoRefreshNumber), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_Timing_Init), (146 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Disable), (16 bytes).
    Removing stm32f4xx_ll_fmc.o(i.FMC_SDRAM_WriteProtection_Enable), (16 bytes).
    Removing stm32f4xx_hal_nor.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_nor.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_nor.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_DeInit), (32 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_16b), (108 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_32b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_8b), (82 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Read_DMA), (112 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Disable), (62 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_WriteOperation_Enable), (60 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_16b), (114 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.HAL_SRAM_Write_DMA), (80 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACplt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMACpltProt), (26 bytes).
    Removing stm32f4xx_hal_sram.o(i.SRAM_DMAError), (26 bytes).
    Removing stm32f4xx_hal_nand.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_nand.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_nand.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pccard.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pccard.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pccard.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_sdram.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_sdram.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_sdram.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (238 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (468 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (134 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (210 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (180 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (528 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (176 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (240 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (544 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (338 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (114 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (208 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (348 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (272 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (116 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (136 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (516 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (288 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (196 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (24 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (88 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (154 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (102 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (148 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (36 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (212 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (188 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (210 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (456 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (272 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (106 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (178 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (32 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (30 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (266 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (92 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (150 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (172 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (100 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (256 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (116 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (12 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (188 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (46 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (176 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (148 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).
    Removing cdcmple.o(.text), (48 bytes).

874 unused section(s) (total 950849 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_nand.c 0x00000000   Number         0  stm32f4xx_hal_nand.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_nor.c 0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pccard.c 0x00000000   Number         0  stm32f4xx_hal_pccard.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sdram.c 0x00000000   Number         0  stm32f4xx_hal_sdram.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalpha_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isalnum_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isxdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isupper_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ispunct_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isprint_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  islower_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isgraph_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isdigit_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  iscntrl_o.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isblank_o.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f_x.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dac.c                        0x00000000   Number         0  dac.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\fmc.c                        0x00000000   Number         0  fmc.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac.c 0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dac_ex.c 0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_nand.c 0x00000000   Number         0  stm32f4xx_hal_nand.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_nor.c 0x00000000   Number         0  stm32f4xx_hal_nor.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pccard.c 0x00000000   Number         0  stm32f4xx_hal_pccard.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sdram.c 0x00000000   Number         0  stm32f4xx_hal_sdram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_sram.c 0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_ll_fmc.c 0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    ..\MY_APP\app_pid.c                      0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\MY_APP\scheduler.c                    0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\MY_Algorithms\Src\kalman.c            0x00000000   Number         0  kalman.o ABSOLUTE
    ..\MY_Algorithms\Src\my_fft.c            0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\MY_Algorithms\Src\my_filter.c         0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\MY_Algorithms\Src\phase_measure.c     0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\MY_Communication\Src\hmi_key_handler.c 0x00000000   Number         0  hmi_key_handler.o ABSOLUTE
    ..\MY_Communication\Src\my_hmi.c         0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\MY_Communication\Src\my_usart.c       0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\MY_Communication\Src\my_usart_pack.c  0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\AD9959.c      0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\ad_measure.c  0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\adc_app.c     0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\da_output.c   0x00000000   Number         0  da_output.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\dac_amplitude_calibration.c 0x00000000   Number         0  dac_amplitude_calibration.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\dac_app.c     0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\MY_Hardware_Drivers\Src\key_app.c     0x00000000   Number         0  key_app.o ABSOLUTE
    ..\MY_Utilities\Src\cmd_to_fun.c         0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    ..\\MY_APP\\app_pid.c                    0x00000000   Number         0  app_pid.o ABSOLUTE
    ..\\MY_APP\\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\kalman.c         0x00000000   Number         0  kalman.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_fft.c         0x00000000   Number         0  my_fft.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\my_filter.c      0x00000000   Number         0  my_filter.o ABSOLUTE
    ..\\MY_Algorithms\\Src\\phase_measure.c  0x00000000   Number         0  phase_measure.o ABSOLUTE
    ..\\MY_Communication\\Src\\hmi_key_handler.c 0x00000000   Number         0  hmi_key_handler.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_hmi.c      0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart.c    0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\\MY_Communication\\Src\\my_usart_pack.c 0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\AD9959.c   0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\ad_measure.c 0x00000000   Number         0  ad_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\adc_app.c  0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\da_output.c 0x00000000   Number         0  da_output.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\dac_amplitude_calibration.c 0x00000000   Number         0  dac_amplitude_calibration.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\dac_app.c  0x00000000   Number         0  dac_app.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\freq_measure.c 0x00000000   Number         0  freq_measure.o ABSOLUTE
    ..\\MY_Hardware_Drivers\\Src\\key_app.c  0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\MY_Utilities\\Src\\cmd_to_fun.c      0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_cos_f32.c                            0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    arm_fir_init_f32.c                       0x00000000   Number         0  arm_fir_init_f32.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c4   Section       36  startup_stm32f429xx.o(.text)
    $v0                                      0x080001c4   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080001e8   Section        0  uldiv.o(.text)
    .text                                    0x0800024a   Section        0  memcpya.o(.text)
    .text                                    0x0800026e   Section        0  memseta.o(.text)
    .text                                    0x08000292   Section        0  atoi.o(.text)
    .text                                    0x080002ac   Section        0  dmul.o(.text)
    .text                                    0x08000390   Section        0  dfltui.o(.text)
    .text                                    0x080003aa   Section        0  ffixul.o(.text)
    .text                                    0x080003d6   Section        0  dfixui.o(.text)
    .text                                    0x08000408   Section        0  f2d.o(.text)
    .text                                    0x0800042e   Section        0  uidiv.o(.text)
    .text                                    0x0800045a   Section        0  llshl.o(.text)
    .text                                    0x08000478   Section        0  llushr.o(.text)
    .text                                    0x08000498   Section        0  strtol.o(.text)
    .text                                    0x08000508   Section        0  frnd.o(.text)
    .text                                    0x08000508   Section        0  iusefp.o(.text)
    .text                                    0x08000544   Section        0  depilogue.o(.text)
    .text                                    0x080005fe   Section        0  dadd.o(.text)
    .text                                    0x0800074c   Section        0  ddiv.o(.text)
    .text                                    0x0800082a   Section        0  dfixul.o(.text)
    .text                                    0x0800085c   Section       48  cdrcmple.o(.text)
    .text                                    0x0800088c   Section       36  init.o(.text)
    .text                                    0x080008b0   Section        0  llsshr.o(.text)
    .text                                    0x080008d4   Section        0  ctype_o.o(.text)
    .text                                    0x080008dc   Section        0  _strtoul.o(.text)
    .text                                    0x0800097a   Section        0  fepilogue.o(.text)
    .text                                    0x080009e8   Section        0  _chval.o(.text)
    [Anonymous Symbol]                       0x08000a04   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x08000ac2   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x08000b04   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x08000b98   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08000cec   Section        0  arm_cos_f32.o(.text.arm_cos_f32)
    [Anonymous Symbol]                       0x08000d84   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x080010e0   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    i.AD9959_CS_H                            0x0800145c   Section        0  ad9959.o(i.AD9959_CS_H)
    i.AD9959_CS_L                            0x0800146c   Section        0  ad9959.o(i.AD9959_CS_L)
    i.AD9959_Ch                              0x0800147c   Section        0  ad9959.o(i.AD9959_Ch)
    i.AD9959_IO_UpDate                       0x080014a4   Section        0  ad9959.o(i.AD9959_IO_UpDate)
    i.AD9959_Init                            0x080014cc   Section        0  ad9959.o(i.AD9959_Init)
    i.AD9959_PDC_L                           0x08001508   Section        0  ad9959.o(i.AD9959_PDC_L)
    i.AD9959_RST_H                           0x08001518   Section        0  ad9959.o(i.AD9959_RST_H)
    i.AD9959_RST_L                           0x08001528   Section        0  ad9959.o(i.AD9959_RST_L)
    i.AD9959_Reset                           0x08001538   Section        0  ad9959.o(i.AD9959_Reset)
    i.AD9959_SCK_H                           0x08001558   Section        0  ad9959.o(i.AD9959_SCK_H)
    i.AD9959_SCK_L                           0x08001568   Section        0  ad9959.o(i.AD9959_SCK_L)
    i.AD9959_SDIO0_H                         0x08001578   Section        0  ad9959.o(i.AD9959_SDIO0_H)
    i.AD9959_SDIO0_L                         0x08001588   Section        0  ad9959.o(i.AD9959_SDIO0_L)
    i.AD9959_SDIO3_H                         0x08001598   Section        0  ad9959.o(i.AD9959_SDIO3_H)
    i.AD9959_SDIO3_L                         0x080015a8   Section        0  ad9959.o(i.AD9959_SDIO3_L)
    i.AD9959_Set_Amp                         0x080015b8   Section        0  ad9959.o(i.AD9959_Set_Amp)
    i.AD9959_Set_Fre                         0x080015cc   Section        0  ad9959.o(i.AD9959_Set_Fre)
    i.AD9959_Set_Pha                         0x080015fc   Section        0  ad9959.o(i.AD9959_Set_Pha)
    i.AD9959_Single_Output                   0x08001620   Section        0  ad9959.o(i.AD9959_Single_Output)
    i.AD9959_Start                           0x0800164e   Section        0  ad9959.o(i.AD9959_Start)
    i.AD9959_UP_H                            0x08001668   Section        0  ad9959.o(i.AD9959_UP_H)
    i.AD9959_UP_L                            0x08001678   Section        0  ad9959.o(i.AD9959_UP_L)
    i.AD9959_WByte                           0x08001688   Section        0  ad9959.o(i.AD9959_WByte)
    i.AD9959_WRrg                            0x080016b8   Section        0  ad9959.o(i.AD9959_WRrg)
    i.ADC_IRQHandler                         0x080016fc   Section        0  stm32f4xx_it.o(i.ADC_IRQHandler)
    i.ADC_Init                               0x08001708   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08001709   Thumb Code   298  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.AD_FIFO_READ_DISABLE                   0x08001838   Section        0  cmd_to_fun.o(i.AD_FIFO_READ_DISABLE)
    i.AD_FIFO_READ_ENABLE                    0x08001856   Section        0  cmd_to_fun.o(i.AD_FIFO_READ_ENABLE)
    i.AD_FIFO_WRITE_DISABLE                  0x08001874   Section        0  cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE)
    i.AD_FIFO_WRITE_ENABLE                   0x08001892   Section        0  cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE)
    i.AD_FREQ_SET                            0x080018bc   Section        0  cmd_to_fun.o(i.AD_FREQ_SET)
    i.Analyze_Filter_Response                0x080018e8   Section        0  hmi_key_handler.o(i.Analyze_Filter_Response)
    Analyze_Filter_Response                  0x080018e9   Thumb Code   748  hmi_key_handler.o(i.Analyze_Filter_Response)
    i.BusFault_Handler                       0x08001e30   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.CTRL_INIT                              0x08001e32   Section        0  cmd_to_fun.o(i.CTRL_INIT)
    i.DAC_Init                               0x08001e3c   Section        0  dac_app.o(i.DAC_Init)
    i.DAC_SetVoltage                         0x08001e70   Section        0  dac_app.o(i.DAC_SetVoltage)
    i.DA_Apply_Settings                      0x08001eb8   Section        0  da_output.o(i.DA_Apply_Settings)
    i.DA_FPGA_START                          0x08001f98   Section        0  cmd_to_fun.o(i.DA_FPGA_START)
    i.DA_FPGA_STOP                           0x08001fa6   Section        0  cmd_to_fun.o(i.DA_FPGA_STOP)
    i.DA_Init                                0x08001fb4   Section        0  da_output.o(i.DA_Init)
    i.DA_SetConfig                           0x08001fec   Section        0  da_output.o(i.DA_SetConfig)
    i.DDS_Disable_Output                     0x0800200c   Section        0  hmi_key_handler.o(i.DDS_Disable_Output)
    DDS_Disable_Output                       0x0800200d   Thumb Code    30  hmi_key_handler.o(i.DDS_Disable_Output)
    i.DDS_Enable_Output                      0x08002030   Section        0  hmi_key_handler.o(i.DDS_Enable_Output)
    DDS_Enable_Output                        0x08002031   Thumb Code    32  hmi_key_handler.o(i.DDS_Enable_Output)
    i.DDS_Update_Display                     0x08002054   Section        0  hmi_key_handler.o(i.DDS_Update_Display)
    DDS_Update_Display                       0x08002055   Thumb Code   512  hmi_key_handler.o(i.DDS_Update_Display)
    i.DMA2_Stream0_IRQHandler                0x08002298   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080022a4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080022a5   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x080022d8   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080022d9   Thumb Code   126  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DebugMon_Handler                       0x08002356   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x08002358   Section        0  main.o(i.Error_Handler)
    i.FMC_NORSRAM_Extended_Timing_Init       0x0800235c   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    i.FMC_NORSRAM_Init                       0x080023ac   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    i.FMC_NORSRAM_Timing_Init                0x0800243c   Section        0  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    i.HAL_ADCEx_InjectedConvCpltCallback     0x080024a6   Section        0  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    i.HAL_ADC_ConfigChannel                  0x080024a8   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002634   Section        0  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x08002650   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_IRQHandler                     0x08002652   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    i.HAL_ADC_Init                           0x08002794   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_LevelOutOfWindowCallback       0x080027ea   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    i.HAL_ADC_MspInit                        0x080027ec   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Stop_DMA                       0x08002894   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DAC_ConfigChannel                  0x08002904   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    i.HAL_DAC_Init                           0x08002966   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    i.HAL_DAC_MspInit                        0x08002990   Section        0  dac.o(i.HAL_DAC_MspInit)
    i.HAL_DAC_SetValue                       0x080029f0   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    i.HAL_DAC_Start                          0x08002a26   Section        0  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    i.HAL_DMA_Abort                          0x08002a98   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002b3a   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002b60   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002d4c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_Delay                              0x08002e38   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_FMC_MspInit                        0x08002e60   Section        0  fmc.o(i.HAL_FMC_MspInit)
    HAL_FMC_MspInit                          0x08002e61   Thumb Code   138  fmc.o(i.HAL_FMC_MspInit)
    i.HAL_GPIO_Init                          0x08002f04   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08003150   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800315e   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800316c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08003178   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003188   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080031bc   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003200   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003230   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x0800324c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080032b4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_EnableOverDrive              0x080032d8   Section        0  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    i.HAL_RCC_CSSCallback                    0x08003354   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    i.HAL_RCC_ClockConfig                    0x08003358   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_EnableCSS                      0x080034d4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    i.HAL_RCC_GetHCLKFreq                    0x080034e0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x080034ec   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800350c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800352c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_NMI_IRQHandler                 0x0800359c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    i.HAL_RCC_OscConfig                      0x080035bc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SRAM_Init                          0x08003a3e   Section        0  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    i.HAL_SRAM_MspInit                       0x08003a9c   Section        0  fmc.o(i.HAL_SRAM_MspInit)
    i.HAL_SYSTICK_Config                     0x08003aa0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003ac8   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003b62   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003bc0   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08003c00   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_UARTEx_RxEventCallback             0x08003cea   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08003cec   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003cf0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08003f70   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003fd8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x080040f4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004110   Section        0  my_usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x0800427c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004334   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HMI_DDS_Display_Update                 0x08004336   Section        0  hmi_key_handler.o(i.HMI_DDS_Display_Update)
    i.HMI_Key_Process                        0x0800433c   Section        0  hmi_key_handler.o(i.HMI_Key_Process)
    i.HMI_Send_String                        0x080045c4   Section        0  my_hmi.o(i.HMI_Send_String)
    i.HMI_Set_DAC_Voltage                    0x08004640   Section        0  hmi_key_handler.o(i.HMI_Set_DAC_Voltage)
    i.HMI_Set_DDS_Frequency                  0x08004668   Section        0  hmi_key_handler.o(i.HMI_Set_DDS_Frequency)
    i.HardFault_Handler                      0x080046a4   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x080046a8   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DAC_Init                            0x0800470c   Section        0  dac.o(i.MX_DAC_Init)
    i.MX_DMA_Init                            0x08004748   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FMC_Init                            0x08004774   Section        0  fmc.o(i.MX_FMC_Init)
    i.MX_GPIO_Init                           0x080047e8   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM3_Init                           0x08004944   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM6_Init                           0x080049ac   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_USART1_UART_Init                    0x080049f4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004a2c   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004a64   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MemManage_Handler                      0x08004a9c   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004a9e   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_Init                               0x08004aa4   Section        0  app_pid.o(i.PID_Init)
    i.ParseDataToVariables                   0x08004af4   Section        0  my_usart_pack.o(i.ParseDataToVariables)
    ParseDataToVariables                     0x08004af5   Thumb Code   150  my_usart_pack.o(i.ParseDataToVariables)
    i.ParseFrame                             0x08004b94   Section        0  my_usart_pack.o(i.ParseFrame)
    i.PendSV_Handler                         0x08004bd6   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Process_Amplitude_Command              0x08004bd8   Section        0  hmi_key_handler.o(i.Process_Amplitude_Command)
    Process_Amplitude_Command                0x08004bd9   Thumb Code   214  hmi_key_handler.o(i.Process_Amplitude_Command)
    i.Process_Parameter_Command              0x08004d48   Section        0  hmi_key_handler.o(i.Process_Parameter_Command)
    Process_Parameter_Command                0x08004d49   Thumb Code   146  hmi_key_handler.o(i.Process_Parameter_Command)
    i.SVC_Handler                            0x08004dec   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Set_Frequency_1kHz                     0x08004df0   Section        0  hmi_key_handler.o(i.Set_Frequency_1kHz)
    Set_Frequency_1kHz                       0x08004df1   Thumb Code    64  hmi_key_handler.o(i.Set_Frequency_1kHz)
    i.Start_Spectrum_Analysis                0x08004e60   Section        0  hmi_key_handler.o(i.Start_Spectrum_Analysis)
    Start_Spectrum_Analysis                  0x08004e61   Thumb Code   612  hmi_key_handler.o(i.Start_Spectrum_Analysis)
    i.SysTick_Handler                        0x08005344   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005348   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080053e8   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x080053f8   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x080054cc   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080054e2   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080054e3   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x080054f2   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x080054f3   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005518   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005519   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08005540   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005541   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x0800554e   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800554f   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x0800559c   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x0800559d   Thumb Code    26  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_Receive_IT                        0x080055b6   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080055b7   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005680   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005681   Thumb Code   248  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x0800577c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_Transmit_IT                       0x080057b4   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x080057b5   Thumb Code    94  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08005812   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08005813   Thumb Code   142  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080058a0   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080058ac   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x080058b8   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.UsageFault_Handler                     0x080058c4   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.Voltage_Step_Down                      0x080058c8   Section        0  hmi_key_handler.o(i.Voltage_Step_Down)
    Voltage_Step_Down                        0x080058c9   Thumb Code   344  hmi_key_handler.o(i.Voltage_Step_Down)
    i.Voltage_Step_Up                        0x08005b20   Section        0  hmi_key_handler.o(i.Voltage_Step_Up)
    Voltage_Step_Up                          0x08005b21   Thumb Code   342  hmi_key_handler.o(i.Voltage_Step_Up)
    i.__0sprintf                             0x08005d78   Section        0  printfa.o(i.__0sprintf)
    i.__0vsnprintf                           0x08005da0   Section        0  printfa.o(i.__0vsnprintf)
    i.__aeabi_errno_addr                     0x08005dd4   Section        0  errno.o(i.__aeabi_errno_addr)
    i.__hardfp_log10f                        0x08005ddc   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_roundf                        0x08005f5c   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sqrtf                         0x08005ff6   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x08006030   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08006044   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x0800604c   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__scatterload_copy                     0x0800605c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800606a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800606c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800607c   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x08006088   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08006089   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x0800620c   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x0800620d   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x080068c0   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x080068c1   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x080068e4   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x080068e5   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08006912   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08006913   Thumb Code    22  printfa.o(i._snputc)
    i._sputc                                 0x08006928   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08006929   Thumb Code    10  printfa.o(i._sputc)
    i.calculate_fft_spectrum                 0x08006934   Section        0  my_fft.o(i.calculate_fft_spectrum)
    i.dac_calibration_get_dac_value          0x08006a00   Section        0  dac_amplitude_calibration.o(i.dac_calibration_get_dac_value)
    i.dac_calibration_init                   0x08006a64   Section        0  dac_amplitude_calibration.o(i.dac_calibration_init)
    i.dac_calibration_interpolate_dac_value  0x08006aac   Section        0  dac_amplitude_calibration.o(i.dac_calibration_interpolate_dac_value)
    i.dac_calibration_is_supported           0x08006b70   Section        0  dac_amplitude_calibration.o(i.dac_calibration_is_supported)
    i.fft_init                               0x08006bb4   Section        0  my_fft.o(i.fft_init)
    i.findMinMax                             0x08006bd0   Section        0  ad_measure.o(i.findMinMax)
    i.generate_hanning_window                0x08006c00   Section        0  my_fft.o(i.generate_hanning_window)
    i.key_proc                               0x08006c60   Section        0  key_app.o(i.key_proc)
    i.key_read                               0x08006ce8   Section        0  key_app.o(i.key_read)
    i.main                                   0x08006d2c   Section        0  main.o(i.main)
    i.my_printf                              0x08006dfc   Section        0  my_usart.o(i.my_printf)
    i.readFIFOData                           0x08006e30   Section        0  ad_measure.o(i.readFIFOData)
    i.scheduler_init                         0x08006eac   Section        0  scheduler.o(i.scheduler_init)
    i.scheduler_run                          0x08006eb8   Section        0  scheduler.o(i.scheduler_run)
    i.setSamplingFrequency                   0x08006ef4   Section        0  ad_measure.o(i.setSamplingFrequency)
    i.vpp_adc_parallel                       0x08006f94   Section        0  ad_measure.o(i.vpp_adc_parallel)
    .constdata                               0x080070b8   Section     1980  dac_amplitude_calibration.o(.constdata)
    calibration_data                         0x080070b8   Data        1980  dac_amplitude_calibration.o(.constdata)
    .constdata                               0x08007874   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08007874   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800787c   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800788c   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08007894   Section       64  log10f.o(.constdata)
    logahi                                   0x08007894   Data          32  log10f.o(.constdata)
    logalo                                   0x080078b4   Data          32  log10f.o(.constdata)
    .constdata                               0x080078d4   Section      129  ctype_o.o(.constdata)
    .constdata                               0x08007958   Section        4  ctype_o.o(.constdata)
    table                                    0x08007958   Data           4  ctype_o.o(.constdata)
    .conststring                             0x0800795c   Section      171  hmi_key_handler.o(.conststring)
    .data                                    0x20000000   Section        8  fmc.o(.data)
    FMC_Initialized                          0x20000000   Data           4  fmc.o(.data)
    FMC_DeInitialized                        0x20000004   Data           4  fmc.o(.data)
    .data                                    0x20000008   Section       16  ad_measure.o(.data)
    .data                                    0x20000018   Section       33  ad9959.o(.data)
    .data                                    0x2000003c   Section       16  key_app.o(.data)
    current_ad_freq                          0x20000040   Data           4  key_app.o(.data)
    current_dac_waveform                     0x20000044   Data           4  key_app.o(.data)
    test_frequency                           0x20000048   Data           4  key_app.o(.data)
    .data                                    0x2000004c   Section        1  adc_app.o(.data)
    .data                                    0x2000004e   Section        2  dac_app.o(.data)
    current_voltage_mv                       0x2000004e   Data           2  dac_app.o(.data)
    .data                                    0x20000050   Section       12  dac_amplitude_calibration.o(.data)
    calibration_initialized                  0x20000050   Data           1  dac_amplitude_calibration.o(.data)
    frequencies_extracted                    0x20000051   Data           1  dac_amplitude_calibration.o(.data)
    unique_count                             0x20000052   Data           2  dac_amplitude_calibration.o(.data)
    calibration_table                        0x20000054   Data           8  dac_amplitude_calibration.o(.data)
    .data                                    0x2000005c   Section       16  my_usart.o(.data)
    .data                                    0x2000006c   Section        2  my_usart_pack.o(.data)
    variableCount                            0x2000006c   Data           2  my_usart_pack.o(.data)
    .data                                    0x20000070   Section       48  hmi_key_handler.o(.data)
    dds_output_enabled                       0x20000070   Data           1  hmi_key_handler.o(.data)
    dds_channel                              0x20000071   Data           1  hmi_key_handler.o(.data)
    dds_waveform_type                        0x20000072   Data           1  hmi_key_handler.o(.data)
    calibration_mode_enabled                 0x20000073   Data           1  hmi_key_handler.o(.data)
    len1                                     0x20000074   Data           1  hmi_key_handler.o(.data)
    dac_voltage                              0x20000076   Data           2  hmi_key_handler.o(.data)
    calibrated_amplitude_mv                  0x20000078   Data           2  hmi_key_handler.o(.data)
    dds_frequency                            0x2000007c   Data           4  hmi_key_handler.o(.data)
    dds_phase                                0x20000080   Data           4  hmi_key_handler.o(.data)
    temp_int                                 0x2000008c   Data           4  hmi_key_handler.o(.data)
    i                                        0x20000090   Data           4  hmi_key_handler.o(.data)
    waveform_names                           0x20000094   Data          12  hmi_key_handler.o(.data)
    .data                                    0x200000a0   Section       40  scheduler.o(.data)
    scheduler_task                           0x200000a4   Data          36  scheduler.o(.data)
    .data                                    0x200000c8   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x200000d4   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x200000d8   Section        4  errno.o(.data)
    _errno                                   0x200000d8   Data           4  errno.o(.data)
    .bss                                     0x200000dc   Section      168  adc.o(.bss)
    .bss                                     0x20000184   Section       20  dac.o(.bss)
    .bss                                     0x20000198   Section       80  fmc.o(.bss)
    .bss                                     0x200001e8   Section      144  tim.o(.bss)
    .bss                                     0x20000278   Section      216  usart.o(.bss)
    .bss                                     0x20000350   Section    12288  ad_measure.o(.bss)
    .bss                                     0x20003350   Section       24  da_output.o(.bss)
    .bss                                     0x20003368   Section    16444  my_fft.o(.bss)
    .bss                                     0x200073a4   Section      512  my_usart.o(.bss)
    .bss                                     0x200075a4   Section       52  my_usart_pack.o(.bss)
    parseTemplate                            0x200075a4   Data          10  my_usart_pack.o(.bss)
    variableMapping                          0x200075b0   Data          40  my_usart_pack.o(.bss)
    .bss                                     0x200075d8   Section     3232  hmi_key_handler.o(.bss)
    vpp_str                                  0x200075d8   Data          16  hmi_key_handler.o(.bss)
    frequency_points                         0x200075e8   Data         804  hmi_key_handler.o(.bss)
    magnitude_response                       0x2000790c   Data         804  hmi_key_handler.o(.bss)
    magnitude_sum                            0x20007c30   Data         804  hmi_key_handler.o(.bss)
    magnitude_avg                            0x20007f54   Data         804  hmi_key_handler.o(.bss)
    .bss                                     0x20008278   Section       36  app_pid.o(.bss)
    STACK                                    0x200082a0   Section     1024  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001c5   Thumb Code     8  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080001df   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x080001e9   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x0800024b   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800024b   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800026f   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800026f   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800027d   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800027d   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800027d   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000281   Thumb Code    18  memseta.o(.text)
    atoi                                     0x08000293   Thumb Code    26  atoi.o(.text)
    __aeabi_dmul                             0x080002ad   Thumb Code   228  dmul.o(.text)
    __aeabi_ui2d                             0x08000391   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2ulz                            0x080003ab   Thumb Code    44  ffixul.o(.text)
    __aeabi_d2uiz                            0x080003d7   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x08000409   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x0800042f   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x0800042f   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0800045b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800045b   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000479   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000479   Thumb Code     0  llushr.o(.text)
    strtol                                   0x08000499   Thumb Code   112  strtol.o(.text)
    __I$use$fp                               0x08000509   Thumb Code     0  iusefp.o(.text)
    _frnd                                    0x08000509   Thumb Code    60  frnd.o(.text)
    _double_round                            0x08000545   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000563   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x080005ff   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000741   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000747   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x0800074d   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800082b   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x0800085d   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x0800088d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800088d   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080008b1   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080008b1   Thumb Code     0  llsshr.o(.text)
    __rt_ctype_table                         0x080008d5   Thumb Code     4  ctype_o.o(.text)
    _strtoul                                 0x080008dd   Thumb Code   158  _strtoul.o(.text)
    _float_round                             0x0800097b   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800098d   Thumb Code    92  fepilogue.o(.text)
    _chval                                   0x080009e9   Thumb Code    28  _chval.o(.text)
    arm_bitreversal_f32                      0x08000a05   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x08000ac3   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x08000b05   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x08000b99   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_cos_f32                              0x08000ced   Thumb Code   152  arm_cos_f32.o(.text.arm_cos_f32)
    arm_radix4_butterfly_f32                 0x08000d85   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x080010e1   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    AD9959_CS_H                              0x0800145d   Thumb Code    10  ad9959.o(i.AD9959_CS_H)
    AD9959_CS_L                              0x0800146d   Thumb Code    12  ad9959.o(i.AD9959_CS_L)
    AD9959_Ch                                0x0800147d   Thumb Code    40  ad9959.o(i.AD9959_Ch)
    AD9959_IO_UpDate                         0x080014a5   Thumb Code    40  ad9959.o(i.AD9959_IO_UpDate)
    AD9959_Init                              0x080014cd   Thumb Code    58  ad9959.o(i.AD9959_Init)
    AD9959_PDC_L                             0x08001509   Thumb Code    12  ad9959.o(i.AD9959_PDC_L)
    AD9959_RST_H                             0x08001519   Thumb Code    10  ad9959.o(i.AD9959_RST_H)
    AD9959_RST_L                             0x08001529   Thumb Code    12  ad9959.o(i.AD9959_RST_L)
    AD9959_Reset                             0x08001539   Thumb Code    30  ad9959.o(i.AD9959_Reset)
    AD9959_SCK_H                             0x08001559   Thumb Code    10  ad9959.o(i.AD9959_SCK_H)
    AD9959_SCK_L                             0x08001569   Thumb Code    10  ad9959.o(i.AD9959_SCK_L)
    AD9959_SDIO0_H                           0x08001579   Thumb Code    10  ad9959.o(i.AD9959_SDIO0_H)
    AD9959_SDIO0_L                           0x08001589   Thumb Code    12  ad9959.o(i.AD9959_SDIO0_L)
    AD9959_SDIO3_H                           0x08001599   Thumb Code    10  ad9959.o(i.AD9959_SDIO3_H)
    AD9959_SDIO3_L                           0x080015a9   Thumb Code    10  ad9959.o(i.AD9959_SDIO3_L)
    AD9959_Set_Amp                           0x080015b9   Thumb Code    20  ad9959.o(i.AD9959_Set_Amp)
    AD9959_Set_Fre                           0x080015cd   Thumb Code    38  ad9959.o(i.AD9959_Set_Fre)
    AD9959_Set_Pha                           0x080015fd   Thumb Code    32  ad9959.o(i.AD9959_Set_Pha)
    AD9959_Single_Output                     0x08001621   Thumb Code    46  ad9959.o(i.AD9959_Single_Output)
    AD9959_Start                             0x0800164f   Thumb Code    26  ad9959.o(i.AD9959_Start)
    AD9959_UP_H                              0x08001669   Thumb Code    10  ad9959.o(i.AD9959_UP_H)
    AD9959_UP_L                              0x08001679   Thumb Code    12  ad9959.o(i.AD9959_UP_L)
    AD9959_WByte                             0x08001689   Thumb Code    48  ad9959.o(i.AD9959_WByte)
    AD9959_WRrg                              0x080016b9   Thumb Code    64  ad9959.o(i.AD9959_WRrg)
    ADC_IRQHandler                           0x080016fd   Thumb Code     6  stm32f4xx_it.o(i.ADC_IRQHandler)
    AD_FIFO_READ_DISABLE                     0x08001839   Thumb Code    30  cmd_to_fun.o(i.AD_FIFO_READ_DISABLE)
    AD_FIFO_READ_ENABLE                      0x08001857   Thumb Code    30  cmd_to_fun.o(i.AD_FIFO_READ_ENABLE)
    AD_FIFO_WRITE_DISABLE                    0x08001875   Thumb Code    30  cmd_to_fun.o(i.AD_FIFO_WRITE_DISABLE)
    AD_FIFO_WRITE_ENABLE                     0x08001893   Thumb Code    42  cmd_to_fun.o(i.AD_FIFO_WRITE_ENABLE)
    AD_FREQ_SET                              0x080018bd   Thumb Code    42  cmd_to_fun.o(i.AD_FREQ_SET)
    BusFault_Handler                         0x08001e31   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    CTRL_INIT                                0x08001e33   Thumb Code    10  cmd_to_fun.o(i.CTRL_INIT)
    DAC_Init                                 0x08001e3d   Thumb Code    24  dac_app.o(i.DAC_Init)
    DAC_SetVoltage                           0x08001e71   Thumb Code    62  dac_app.o(i.DAC_SetVoltage)
    DA_Apply_Settings                        0x08001eb9   Thumb Code   206  da_output.o(i.DA_Apply_Settings)
    DA_FPGA_START                            0x08001f99   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_START)
    DA_FPGA_STOP                             0x08001fa7   Thumb Code    14  cmd_to_fun.o(i.DA_FPGA_STOP)
    DA_Init                                  0x08001fb5   Thumb Code    46  da_output.o(i.DA_Init)
    DA_SetConfig                             0x08001fed   Thumb Code    28  da_output.o(i.DA_SetConfig)
    DMA2_Stream0_IRQHandler                  0x08002299   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DebugMon_Handler                         0x08002357   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x08002359   Thumb Code     4  main.o(i.Error_Handler)
    FMC_NORSRAM_Extended_Timing_Init         0x0800235d   Thumb Code    76  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Extended_Timing_Init)
    FMC_NORSRAM_Init                         0x080023ad   Thumb Code   140  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Init)
    FMC_NORSRAM_Timing_Init                  0x0800243d   Thumb Code   106  stm32f4xx_ll_fmc.o(i.FMC_NORSRAM_Timing_Init)
    HAL_ADCEx_InjectedConvCpltCallback       0x080024a7   Thumb Code     2  stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback)
    HAL_ADC_ConfigChannel                    0x080024a9   Thumb Code   374  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002635   Thumb Code    20  adc_app.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ErrorCallback                    0x08002651   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_IRQHandler                       0x08002653   Thumb Code   322  stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler)
    HAL_ADC_Init                             0x08002795   Thumb Code    86  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_LevelOutOfWindowCallback         0x080027eb   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback)
    HAL_ADC_MspInit                          0x080027ed   Thumb Code   146  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Stop_DMA                         0x08002895   Thumb Code   112  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DAC_ConfigChannel                    0x08002905   Thumb Code    98  stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel)
    HAL_DAC_Init                             0x08002967   Thumb Code    42  stm32f4xx_hal_dac.o(i.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x08002991   Thumb Code    84  dac.o(i.HAL_DAC_MspInit)
    HAL_DAC_SetValue                         0x080029f1   Thumb Code    54  stm32f4xx_hal_dac.o(i.HAL_DAC_SetValue)
    HAL_DAC_Start                            0x08002a27   Thumb Code   114  stm32f4xx_hal_dac.o(i.HAL_DAC_Start)
    HAL_DMA_Abort                            0x08002a99   Thumb Code   162  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002b3b   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002b61   Thumb Code   488  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002d4d   Thumb Code   230  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_Delay                                0x08002e39   Thumb Code    34  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002f05   Thumb Code   564  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08003151   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800315f   Thumb Code    14  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800316d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08003179   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003189   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080031bd   Thumb Code    58  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003201   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003231   Thumb Code    28  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x0800324d   Thumb Code    98  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080032b5   Thumb Code    30  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x080032d9   Thumb Code   110  stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive)
    HAL_RCC_CSSCallback                      0x08003355   Thumb Code     2  stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback)
    HAL_RCC_ClockConfig                      0x08003359   Thumb Code   354  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_EnableCSS                        0x080034d5   Thumb Code     8  stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS)
    HAL_RCC_GetHCLKFreq                      0x080034e1   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x080034ed   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800350d   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800352d   Thumb Code    94  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_NMI_IRQHandler                   0x0800359d   Thumb Code    24  stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler)
    HAL_RCC_OscConfig                        0x080035bd   Thumb Code  1154  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x08003a3f   Thumb Code    94  stm32f4xx_hal_sram.o(i.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08003a9d   Thumb Code     4  fmc.o(i.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x08003aa1   Thumb Code    36  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08003ac9   Thumb Code   154  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003b63   Thumb Code    92  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003bc1   Thumb Code    52  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08003c01   Thumb Code   234  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_UARTEx_RxEventCallback               0x08003ceb   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003ced   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003cf1   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003f71   Thumb Code   102  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003fd9   Thumb Code   258  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080040f5   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004111   Thumb Code   328  my_usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x0800427d   Thumb Code   184  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004335   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HMI_DDS_Display_Update                   0x08004337   Thumb Code     4  hmi_key_handler.o(i.HMI_DDS_Display_Update)
    HMI_Key_Process                          0x0800433d   Thumb Code   228  hmi_key_handler.o(i.HMI_Key_Process)
    HMI_Send_String                          0x080045c5   Thumb Code   106  my_hmi.o(i.HMI_Send_String)
    HMI_Set_DAC_Voltage                      0x08004641   Thumb Code    34  hmi_key_handler.o(i.HMI_Set_DAC_Voltage)
    HMI_Set_DDS_Frequency                    0x08004669   Thumb Code    50  hmi_key_handler.o(i.HMI_Set_DDS_Frequency)
    HardFault_Handler                        0x080046a5   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x080046a9   Thumb Code    86  adc.o(i.MX_ADC1_Init)
    MX_DAC_Init                              0x0800470d   Thumb Code    50  dac.o(i.MX_DAC_Init)
    MX_DMA_Init                              0x08004749   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_FMC_Init                              0x08004775   Thumb Code   108  fmc.o(i.MX_FMC_Init)
    MX_GPIO_Init                             0x080047e9   Thumb Code   318  gpio.o(i.MX_GPIO_Init)
    MX_TIM3_Init                             0x08004945   Thumb Code    96  tim.o(i.MX_TIM3_Init)
    MX_TIM6_Init                             0x080049ad   Thumb Code    62  tim.o(i.MX_TIM6_Init)
    MX_USART1_UART_Init                      0x080049f5   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004a2d   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004a65   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MemManage_Handler                        0x08004a9d   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004a9f   Thumb Code     6  stm32f4xx_it.o(i.NMI_Handler)
    PID_Init                                 0x08004aa5   Thumb Code    60  app_pid.o(i.PID_Init)
    ParseFrame                               0x08004b95   Thumb Code    66  my_usart_pack.o(i.ParseFrame)
    PendSV_Handler                           0x08004bd7   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08004ded   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08005345   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005349   Thumb Code   150  main.o(i.SystemClock_Config)
    SystemInit                               0x080053e9   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x080053f9   Thumb Code   190  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x080054cd   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_IT                    0x0800577d   Thumb Code    56  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x080058a1   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080058ad   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x080058b9   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    UsageFault_Handler                       0x080058c5   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0sprintf                               0x08005d79   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x08005d79   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x08005d79   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x08005d79   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x08005d79   Thumb Code     0  printfa.o(i.__0sprintf)
    __0vsnprintf                             0x08005da1   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08005da1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08005da1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08005da1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08005da1   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __aeabi_errno_addr                       0x08005dd5   Thumb Code     4  errno.o(i.__aeabi_errno_addr)
    __rt_errno_addr                          0x08005dd5   Thumb Code     0  errno.o(i.__aeabi_errno_addr)
    __hardfp_log10f                          0x08005ddd   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_roundf                          0x08005f5d   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sqrtf                           0x08005ff7   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x08006031   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08006045   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x0800604d   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __scatterload_copy                       0x0800605d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800606b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800606d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800607d   Thumb Code     6  errno.o(i.__set_errno)
    calculate_fft_spectrum                   0x08006935   Thumb Code   188  my_fft.o(i.calculate_fft_spectrum)
    dac_calibration_get_dac_value            0x08006a01   Thumb Code    90  dac_amplitude_calibration.o(i.dac_calibration_get_dac_value)
    dac_calibration_init                     0x08006a65   Thumb Code    18  dac_amplitude_calibration.o(i.dac_calibration_init)
    dac_calibration_interpolate_dac_value    0x08006aad   Thumb Code   186  dac_amplitude_calibration.o(i.dac_calibration_interpolate_dac_value)
    dac_calibration_is_supported             0x08006b71   Thumb Code    58  dac_amplitude_calibration.o(i.dac_calibration_is_supported)
    fft_init                                 0x08006bb5   Thumb Code    22  my_fft.o(i.fft_init)
    findMinMax                               0x08006bd1   Thumb Code    48  ad_measure.o(i.findMinMax)
    generate_hanning_window                  0x08006c01   Thumb Code    82  my_fft.o(i.generate_hanning_window)
    key_proc                                 0x08006c61   Thumb Code    72  key_app.o(i.key_proc)
    key_read                                 0x08006ce9   Thumb Code    60  key_app.o(i.key_read)
    main                                     0x08006d2d   Thumb Code   134  main.o(i.main)
    my_printf                                0x08006dfd   Thumb Code    50  my_usart.o(i.my_printf)
    readFIFOData                             0x08006e31   Thumb Code   118  ad_measure.o(i.readFIFOData)
    scheduler_init                           0x08006ead   Thumb Code     8  scheduler.o(i.scheduler_init)
    scheduler_run                            0x08006eb9   Thumb Code    56  scheduler.o(i.scheduler_run)
    setSamplingFrequency                     0x08006ef5   Thumb Code   140  ad_measure.o(i.setSamplingFrequency)
    vpp_adc_parallel                         0x08006f95   Thumb Code   266  ad_measure.o(i.vpp_adc_parallel)
    AHBPrescTable                            0x0800787c   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800788c   Data           8  system_stm32f4xx.o(.constdata)
    __ctype_table                            0x080078d4   Data         129  ctype_o.o(.constdata)
    armBitRevTable                           0x08007a08   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    sinTable_f32                             0x08008208   Data        2052  arm_common_tables.o(.rodata.sinTable_f32)
    twiddleCoef_4096                         0x08008a0c   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x08010a0c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08010a2c   Number         0  anon$$obj.o(Region$$Table)
    vol_maxnum1                              0x20000008   Data           2  ad_measure.o(.data)
    vol_minnum1                              0x2000000a   Data           2  ad_measure.o(.data)
    vol_maxnum2                              0x2000000c   Data           2  ad_measure.o(.data)
    vol_minnum2                              0x2000000e   Data           2  ad_measure.o(.data)
    vol_amp1                                 0x20000010   Data           4  ad_measure.o(.data)
    vol_amp2                                 0x20000014   Data           4  ad_measure.o(.data)
    mdoe_flag                                0x20000018   Data           1  ad9959.o(.data)
    Phase                                    0x2000001a   Data           2  ad9959.o(.data)
    pid_vin                                  0x2000001c   Data           4  ad9959.o(.data)
    Reg_Len                                  0x20000020   Data          25  ad9959.o(.data)
    key_val                                  0x2000003c   Data           1  key_app.o(.data)
    key_old                                  0x2000003d   Data           1  key_app.o(.data)
    key_down                                 0x2000003e   Data           1  key_app.o(.data)
    key_up                                   0x2000003f   Data           1  key_app.o(.data)
    AdcConvEnd                               0x2000004c   Data           1  adc_app.o(.data)
    commandReceived1                         0x2000005c   Data           1  my_usart.o(.data)
    commandReceived3                         0x2000005d   Data           1  my_usart.o(.data)
    frameStarted                             0x2000005e   Data           1  my_usart.o(.data)
    rxTemp1                                  0x2000005f   Data           1  my_usart.o(.data)
    rxTemp3                                  0x20000060   Data           1  my_usart.o(.data)
    rxTemp2                                  0x20000061   Data           1  my_usart.o(.data)
    rxIndex1                                 0x20000062   Data           2  my_usart.o(.data)
    rxIndex3                                 0x20000064   Data           2  my_usart.o(.data)
    rxIndex2                                 0x20000066   Data           2  my_usart.o(.data)
    USART_RX_STA                             0x20000068   Data           2  my_usart.o(.data)
    hmi_key_index                            0x2000006a   Data           2  my_usart.o(.data)
    freq_my                                  0x20000084   Data           4  hmi_key_handler.o(.data)
    vpp_my                                   0x20000088   Data           4  hmi_key_handler.o(.data)
    task_num                                 0x200000a0   Data           1  scheduler.o(.data)
    uwTickFreq                               0x200000c8   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x200000cc   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x200000d0   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x200000d4   Data           4  system_stm32f4xx.o(.data)
    hadc1                                    0x200000dc   Data          72  adc.o(.bss)
    hdma_adc1                                0x20000124   Data          96  adc.o(.bss)
    hdac                                     0x20000184   Data          20  dac.o(.bss)
    hsram2                                   0x20000198   Data          80  fmc.o(.bss)
    htim3                                    0x200001e8   Data          72  tim.o(.bss)
    htim6                                    0x20000230   Data          72  tim.o(.bss)
    huart1                                   0x20000278   Data          72  usart.o(.bss)
    huart2                                   0x200002c0   Data          72  usart.o(.bss)
    huart3                                   0x20000308   Data          72  usart.o(.bss)
    fifo_data1                               0x20000350   Data        2048  ad_measure.o(.bss)
    fifo_data2                               0x20000b50   Data        2048  ad_measure.o(.bss)
    fifo_data1_f                             0x20001350   Data        4096  ad_measure.o(.bss)
    fifo_data2_f                             0x20002350   Data        4096  ad_measure.o(.bss)
    da_channels                              0x20003350   Data          24  da_output.o(.bss)
    fft_instance                             0x20003368   Data          20  my_fft.o(.bss)
    fft_input_buffer                         0x2000337c   Data        8192  my_fft.o(.bss)
    fft_magnitude                            0x2000537c   Data        4096  my_fft.o(.bss)
    window_buffer                            0x2000637c   Data        4096  my_fft.o(.bss)
    dual_peaks                               0x2000737c   Data          40  my_fft.o(.bss)
    rxBuffer1                                0x200073a4   Data         128  my_usart.o(.bss)
    rxBuffer3                                0x20007424   Data         128  my_usart.o(.bss)
    rxBuffer2                                0x200074a4   Data         128  my_usart.o(.bss)
    USART_RX_BUF                             0x20007524   Data         128  my_usart.o(.bss)
    PID                                      0x20008278   Data          36  app_pid.o(.bss)
    __initial_sp                             0x200086a0   Data           0  startup_stm32f429xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00010b08, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00010a2c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         5943  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         6282    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         6285    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6287    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6289    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         6290    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         6297    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         6292    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         6294    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         6283    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x00000024   Code   RO            4    .text               startup_stm32f429xx.o
    0x080001e8   0x080001e8   0x00000062   Code   RO         5946    .text               mc_w.l(uldiv.o)
    0x0800024a   0x0800024a   0x00000024   Code   RO         5948    .text               mc_w.l(memcpya.o)
    0x0800026e   0x0800026e   0x00000024   Code   RO         5950    .text               mc_w.l(memseta.o)
    0x08000292   0x08000292   0x0000001a   Code   RO         6215    .text               mc_w.l(atoi.o)
    0x080002ac   0x080002ac   0x000000e4   Code   RO         6217    .text               mf_w.l(dmul.o)
    0x08000390   0x08000390   0x0000001a   Code   RO         6221    .text               mf_w.l(dfltui.o)
    0x080003aa   0x080003aa   0x0000002c   Code   RO         6223    .text               mf_w.l(ffixul.o)
    0x080003d6   0x080003d6   0x00000032   Code   RO         6227    .text               mf_w.l(dfixui.o)
    0x08000408   0x08000408   0x00000026   Code   RO         6229    .text               mf_w.l(f2d.o)
    0x0800042e   0x0800042e   0x0000002c   Code   RO         6301    .text               mc_w.l(uidiv.o)
    0x0800045a   0x0800045a   0x0000001e   Code   RO         6303    .text               mc_w.l(llshl.o)
    0x08000478   0x08000478   0x00000020   Code   RO         6305    .text               mc_w.l(llushr.o)
    0x08000498   0x08000498   0x00000070   Code   RO         6314    .text               mc_w.l(strtol.o)
    0x08000508   0x08000508   0x00000000   Code   RO         6316    .text               mc_w.l(iusefp.o)
    0x08000508   0x08000508   0x0000003c   Code   RO         6317    .text               mf_w.l(frnd.o)
    0x08000544   0x08000544   0x000000ba   Code   RO         6319    .text               mf_w.l(depilogue.o)
    0x080005fe   0x080005fe   0x0000014e   Code   RO         6321    .text               mf_w.l(dadd.o)
    0x0800074c   0x0800074c   0x000000de   Code   RO         6323    .text               mf_w.l(ddiv.o)
    0x0800082a   0x0800082a   0x00000030   Code   RO         6329    .text               mf_w.l(dfixul.o)
    0x0800085a   0x0800085a   0x00000002   PAD
    0x0800085c   0x0800085c   0x00000030   Code   RO         6331    .text               mf_w.l(cdrcmple.o)
    0x0800088c   0x0800088c   0x00000024   Code   RO         6333    .text               mc_w.l(init.o)
    0x080008b0   0x080008b0   0x00000024   Code   RO         6336    .text               mc_w.l(llsshr.o)
    0x080008d4   0x080008d4   0x00000008   Code   RO         6338    .text               mc_w.l(ctype_o.o)
    0x080008dc   0x080008dc   0x0000009e   Code   RO         6366    .text               mc_w.l(_strtoul.o)
    0x0800097a   0x0800097a   0x0000006e   Code   RO         6369    .text               mf_w.l(fepilogue.o)
    0x080009e8   0x080009e8   0x0000001c   Code   RO         6375    .text               mc_w.l(_chval.o)
    0x08000a04   0x08000a04   0x000000be   Code   RO         5766    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08000ac2   0x08000ac2   0x00000040   Code   RO         5743    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08000b02   0x08000b02   0x00000002   PAD
    0x08000b04   0x08000b04   0x00000094   Code   RO         5757    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08000b98   0x08000b98   0x00000154   Code   RO         5724    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08000cec   0x08000cec   0x00000098   Code   RO         5715    .text.arm_cos_f32   arm_cortexM4lf_math.lib(arm_cos_f32.o)
    0x08000d84   0x08000d84   0x0000035a   Code   RO         5747    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x080010de   0x080010de   0x00000002   PAD
    0x080010e0   0x080010e0   0x0000037a   Code   RO         5745    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800145a   0x0800145a   0x00000002   PAD
    0x0800145c   0x0800145c   0x00000010   Code   RO          807    i.AD9959_CS_H       ad9959.o
    0x0800146c   0x0800146c   0x00000010   Code   RO          808    i.AD9959_CS_L       ad9959.o
    0x0800147c   0x0800147c   0x00000028   Code   RO          809    i.AD9959_Ch         ad9959.o
    0x080014a4   0x080014a4   0x00000028   Code   RO          810    i.AD9959_IO_UpDate  ad9959.o
    0x080014cc   0x080014cc   0x0000003a   Code   RO          811    i.AD9959_Init       ad9959.o
    0x08001506   0x08001506   0x00000002   PAD
    0x08001508   0x08001508   0x00000010   Code   RO          821    i.AD9959_PDC_L      ad9959.o
    0x08001518   0x08001518   0x00000010   Code   RO          822    i.AD9959_RST_H      ad9959.o
    0x08001528   0x08001528   0x00000010   Code   RO          823    i.AD9959_RST_L      ad9959.o
    0x08001538   0x08001538   0x0000001e   Code   RO          824    i.AD9959_Reset      ad9959.o
    0x08001556   0x08001556   0x00000002   PAD
    0x08001558   0x08001558   0x00000010   Code   RO          825    i.AD9959_SCK_H      ad9959.o
    0x08001568   0x08001568   0x00000010   Code   RO          826    i.AD9959_SCK_L      ad9959.o
    0x08001578   0x08001578   0x00000010   Code   RO          827    i.AD9959_SDIO0_H    ad9959.o
    0x08001588   0x08001588   0x00000010   Code   RO          828    i.AD9959_SDIO0_L    ad9959.o
    0x08001598   0x08001598   0x00000010   Code   RO          833    i.AD9959_SDIO3_H    ad9959.o
    0x080015a8   0x080015a8   0x00000010   Code   RO          834    i.AD9959_SDIO3_L    ad9959.o
    0x080015b8   0x080015b8   0x00000014   Code   RO          835    i.AD9959_Set_Amp    ad9959.o
    0x080015cc   0x080015cc   0x00000030   Code   RO          836    i.AD9959_Set_Fre    ad9959.o
    0x080015fc   0x080015fc   0x00000024   Code   RO          837    i.AD9959_Set_Pha    ad9959.o
    0x08001620   0x08001620   0x0000002e   Code   RO          838    i.AD9959_Single_Output  ad9959.o
    0x0800164e   0x0800164e   0x0000001a   Code   RO          839    i.AD9959_Start      ad9959.o
    0x08001668   0x08001668   0x00000010   Code   RO          841    i.AD9959_UP_H       ad9959.o
    0x08001678   0x08001678   0x00000010   Code   RO          842    i.AD9959_UP_L       ad9959.o
    0x08001688   0x08001688   0x00000030   Code   RO          843    i.AD9959_WByte      ad9959.o
    0x080016b8   0x080016b8   0x00000044   Code   RO          844    i.AD9959_WRrg       ad9959.o
    0x080016fc   0x080016fc   0x0000000c   Code   RO          622    i.ADC_IRQHandler    stm32f4xx_it.o
    0x08001708   0x08001708   0x00000130   Code   RO         2143    i.ADC_Init          stm32f4xx_hal_adc.o
    0x08001838   0x08001838   0x0000001e   Code   RO         1953    i.AD_FIFO_READ_DISABLE  cmd_to_fun.o
    0x08001856   0x08001856   0x0000001e   Code   RO         1954    i.AD_FIFO_READ_ENABLE  cmd_to_fun.o
    0x08001874   0x08001874   0x0000001e   Code   RO         1955    i.AD_FIFO_WRITE_DISABLE  cmd_to_fun.o
    0x08001892   0x08001892   0x0000002a   Code   RO         1956    i.AD_FIFO_WRITE_ENABLE  cmd_to_fun.o
    0x080018bc   0x080018bc   0x0000002a   Code   RO         1959    i.AD_FREQ_SET       cmd_to_fun.o
    0x080018e6   0x080018e6   0x00000002   PAD
    0x080018e8   0x080018e8   0x00000548   Code   RO         1763    i.Analyze_Filter_Response  hmi_key_handler.o
    0x08001e30   0x08001e30   0x00000002   Code   RO          623    i.BusFault_Handler  stm32f4xx_it.o
    0x08001e32   0x08001e32   0x0000000a   Code   RO         1962    i.CTRL_INIT         cmd_to_fun.o
    0x08001e3c   0x08001e3c   0x00000034   Code   RO         1257    i.DAC_Init          dac_app.o
    0x08001e70   0x08001e70   0x00000048   Code   RO         1258    i.DAC_SetVoltage    dac_app.o
    0x08001eb8   0x08001eb8   0x000000e0   Code   RO         1064    i.DA_Apply_Settings  da_output.o
    0x08001f98   0x08001f98   0x0000000e   Code   RO         1963    i.DA_FPGA_START     cmd_to_fun.o
    0x08001fa6   0x08001fa6   0x0000000e   Code   RO         1964    i.DA_FPGA_STOP      cmd_to_fun.o
    0x08001fb4   0x08001fb4   0x00000038   Code   RO         1065    i.DA_Init           da_output.o
    0x08001fec   0x08001fec   0x00000020   Code   RO         1066    i.DA_SetConfig      da_output.o
    0x0800200c   0x0800200c   0x00000024   Code   RO         1764    i.DDS_Disable_Output  hmi_key_handler.o
    0x08002030   0x08002030   0x00000024   Code   RO         1765    i.DDS_Enable_Output  hmi_key_handler.o
    0x08002054   0x08002054   0x00000244   Code   RO         1766    i.DDS_Update_Display  hmi_key_handler.o
    0x08002298   0x08002298   0x0000000c   Code   RO          624    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x080022a4   0x080022a4   0x00000034   Code   RO         2979    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x080022d8   0x080022d8   0x0000007e   Code   RO         2980    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002356   0x08002356   0x00000002   Code   RO          625    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002358   0x08002358   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x0800235c   0x0800235c   0x00000050   Code   RO         3943    i.FMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fmc.o
    0x080023ac   0x080023ac   0x00000090   Code   RO         3944    i.FMC_NORSRAM_Init  stm32f4xx_ll_fmc.o
    0x0800243c   0x0800243c   0x0000006a   Code   RO         3945    i.FMC_NORSRAM_Timing_Init  stm32f4xx_ll_fmc.o
    0x080024a6   0x080024a6   0x00000002   Code   RO         2314    i.HAL_ADCEx_InjectedConvCpltCallback  stm32f4xx_hal_adc_ex.o
    0x080024a8   0x080024a8   0x0000018c   Code   RO         2145    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08002634   0x08002634   0x0000001c   Code   RO         1213    i.HAL_ADC_ConvCpltCallback  adc_app.o
    0x08002650   0x08002650   0x00000002   Code   RO         2149    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x08002652   0x08002652   0x00000142   Code   RO         2153    i.HAL_ADC_IRQHandler  stm32f4xx_hal_adc.o
    0x08002794   0x08002794   0x00000056   Code   RO         2154    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x080027ea   0x080027ea   0x00000002   Code   RO         2155    i.HAL_ADC_LevelOutOfWindowCallback  stm32f4xx_hal_adc.o
    0x080027ec   0x080027ec   0x000000a8   Code   RO          365    i.HAL_ADC_MspInit   adc.o
    0x08002894   0x08002894   0x00000070   Code   RO         2164    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x08002904   0x08002904   0x00000062   Code   RO         3698    i.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x08002966   0x08002966   0x0000002a   Code   RO         3708    i.HAL_DAC_Init      stm32f4xx_hal_dac.o
    0x08002990   0x08002990   0x00000060   Code   RO          407    i.HAL_DAC_MspInit   dac.o
    0x080029f0   0x080029f0   0x00000036   Code   RO         3711    i.HAL_DAC_SetValue  stm32f4xx_hal_dac.o
    0x08002a26   0x08002a26   0x00000072   Code   RO         3712    i.HAL_DAC_Start     stm32f4xx_hal_dac.o
    0x08002a98   0x08002a98   0x000000a2   Code   RO         2982    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08002b3a   0x08002b3a   0x00000024   Code   RO         2983    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002b5e   0x08002b5e   0x00000002   PAD
    0x08002b60   0x08002b60   0x000001ec   Code   RO         2987    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002d4c   0x08002d4c   0x000000ec   Code   RO         2988    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002e38   0x08002e38   0x00000028   Code   RO         3432    i.HAL_Delay         stm32f4xx_hal.o
    0x08002e60   0x08002e60   0x000000a4   Code   RO          472    i.HAL_FMC_MspInit   fmc.o
    0x08002f04   0x08002f04   0x0000024c   Code   RO         2875    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08003150   0x08003150   0x0000000e   Code   RO         2877    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800315e   0x0800315e   0x0000000e   Code   RO         2879    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x0800316c   0x0800316c   0x0000000c   Code   RO         3440    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003178   0x08003178   0x00000010   Code   RO         3446    i.HAL_IncTick       stm32f4xx_hal.o
    0x08003188   0x08003188   0x00000034   Code   RO         3447    i.HAL_Init          stm32f4xx_hal.o
    0x080031bc   0x080031bc   0x00000044   Code   RO         3448    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003200   0x08003200   0x00000030   Code   RO          728    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003230   0x08003230   0x0000001c   Code   RO         3285    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800324c   0x0800324c   0x00000068   Code   RO         3291    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080032b4   0x080032b4   0x00000024   Code   RO         3292    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080032d8   0x080032d8   0x0000007c   Code   RO         3211    i.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x08003354   0x08003354   0x00000002   Code   RO         2418    i.HAL_RCC_CSSCallback  stm32f4xx_hal_rcc.o
    0x08003356   0x08003356   0x00000002   PAD
    0x08003358   0x08003358   0x0000017c   Code   RO         2419    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080034d4   0x080034d4   0x0000000c   Code   RO         2422    i.HAL_RCC_EnableCSS  stm32f4xx_hal_rcc.o
    0x080034e0   0x080034e0   0x0000000c   Code   RO         2424    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x080034ec   0x080034ec   0x00000020   Code   RO         2426    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800350c   0x0800350c   0x00000020   Code   RO         2427    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800352c   0x0800352c   0x00000070   Code   RO         2428    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800359c   0x0800359c   0x00000020   Code   RO         2430    i.HAL_RCC_NMI_IRQHandler  stm32f4xx_hal_rcc.o
    0x080035bc   0x080035bc   0x00000482   Code   RO         2431    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003a3e   0x08003a3e   0x0000005e   Code   RO         4137    i.HAL_SRAM_Init     stm32f4xx_hal_sram.o
    0x08003a9c   0x08003a9c   0x00000004   Code   RO          474    i.HAL_SRAM_MspInit  fmc.o
    0x08003aa0   0x08003aa0   0x00000028   Code   RO         3296    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003ac8   0x08003ac8   0x0000009a   Code   RO         5058    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003b62   0x08003b62   0x0000005c   Code   RO         4324    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08003bbe   0x08003bbe   0x00000002   PAD
    0x08003bc0   0x08003bc0   0x00000040   Code   RO          521    i.HAL_TIM_Base_MspInit  tim.o
    0x08003c00   0x08003c00   0x000000ea   Code   RO         4333    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003cea   0x08003cea   0x00000002   Code   RO         5318    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08003cec   0x08003cec   0x00000002   Code   RO         5332    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003cee   0x08003cee   0x00000002   PAD
    0x08003cf0   0x08003cf0   0x00000280   Code   RO         5335    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08003f70   0x08003f70   0x00000066   Code   RO         5336    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08003fd6   0x08003fd6   0x00000002   PAD
    0x08003fd8   0x08003fd8   0x0000011c   Code   RO          569    i.HAL_UART_MspInit  usart.o
    0x080040f4   0x080040f4   0x0000001c   Code   RO         5341    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004110   0x08004110   0x0000016c   Code   RO         1665    i.HAL_UART_RxCpltCallback  my_usart.o
    0x0800427c   0x0800427c   0x000000b8   Code   RO         5344    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004334   0x08004334   0x00000002   Code   RO         5347    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004336   0x08004336   0x00000004   Code   RO         1769    i.HMI_DDS_Display_Update  hmi_key_handler.o
    0x0800433a   0x0800433a   0x00000002   PAD
    0x0800433c   0x0800433c   0x00000288   Code   RO         1779    i.HMI_Key_Process   hmi_key_handler.o
    0x080045c4   0x080045c4   0x0000007c   Code   RO         1613    i.HMI_Send_String   my_hmi.o
    0x08004640   0x08004640   0x00000028   Code   RO         1781    i.HMI_Set_DAC_Voltage  hmi_key_handler.o
    0x08004668   0x08004668   0x0000003c   Code   RO         1782    i.HMI_Set_DDS_Frequency  hmi_key_handler.o
    0x080046a4   0x080046a4   0x00000002   Code   RO          626    i.HardFault_Handler  stm32f4xx_it.o
    0x080046a6   0x080046a6   0x00000002   PAD
    0x080046a8   0x080046a8   0x00000064   Code   RO          366    i.MX_ADC1_Init      adc.o
    0x0800470c   0x0800470c   0x0000003c   Code   RO          408    i.MX_DAC_Init       dac.o
    0x08004748   0x08004748   0x0000002c   Code   RO          448    i.MX_DMA_Init       dma.o
    0x08004774   0x08004774   0x00000074   Code   RO          475    i.MX_FMC_Init       fmc.o
    0x080047e8   0x080047e8   0x0000015c   Code   RO          340    i.MX_GPIO_Init      gpio.o
    0x08004944   0x08004944   0x00000068   Code   RO          522    i.MX_TIM3_Init      tim.o
    0x080049ac   0x080049ac   0x00000048   Code   RO          523    i.MX_TIM6_Init      tim.o
    0x080049f4   0x080049f4   0x00000038   Code   RO          570    i.MX_USART1_UART_Init  usart.o
    0x08004a2c   0x08004a2c   0x00000038   Code   RO          571    i.MX_USART2_UART_Init  usart.o
    0x08004a64   0x08004a64   0x00000038   Code   RO          572    i.MX_USART3_UART_Init  usart.o
    0x08004a9c   0x08004a9c   0x00000002   Code   RO          627    i.MemManage_Handler  stm32f4xx_it.o
    0x08004a9e   0x08004a9e   0x00000006   Code   RO          628    i.NMI_Handler       stm32f4xx_it.o
    0x08004aa4   0x08004aa4   0x00000050   Code   RO         2096    i.PID_Init          app_pid.o
    0x08004af4   0x08004af4   0x000000a0   Code   RO         1713    i.ParseDataToVariables  my_usart_pack.o
    0x08004b94   0x08004b94   0x00000042   Code   RO         1714    i.ParseFrame        my_usart_pack.o
    0x08004bd6   0x08004bd6   0x00000002   Code   RO          629    i.PendSV_Handler    stm32f4xx_it.o
    0x08004bd8   0x08004bd8   0x00000170   Code   RO         1786    i.Process_Amplitude_Command  hmi_key_handler.o
    0x08004d48   0x08004d48   0x000000a4   Code   RO         1787    i.Process_Parameter_Command  hmi_key_handler.o
    0x08004dec   0x08004dec   0x00000002   Code   RO          630    i.SVC_Handler       stm32f4xx_it.o
    0x08004dee   0x08004dee   0x00000002   PAD
    0x08004df0   0x08004df0   0x00000070   Code   RO         1788    i.Set_Frequency_1kHz  hmi_key_handler.o
    0x08004e60   0x08004e60   0x000004e4   Code   RO         1789    i.Start_Spectrum_Analysis  hmi_key_handler.o
    0x08005344   0x08005344   0x00000004   Code   RO          631    i.SysTick_Handler   stm32f4xx_it.o
    0x08005348   0x08005348   0x000000a0   Code   RO           14    i.SystemClock_Config  main.o
    0x080053e8   0x080053e8   0x00000010   Code   RO         5680    i.SystemInit        system_stm32f4xx.o
    0x080053f8   0x080053f8   0x000000d4   Code   RO         4417    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080054cc   0x080054cc   0x00000016   Code   RO         4428    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x080054e2   0x080054e2   0x00000010   Code   RO         4429    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x080054f2   0x080054f2   0x00000026   Code   RO         4435    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005518   0x08005518   0x00000028   Code   RO         4437    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005540   0x08005540   0x0000000e   Code   RO         5349    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800554e   0x0800554e   0x0000004e   Code   RO         5359    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x0800559c   0x0800559c   0x0000001a   Code   RO         5360    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x080055b6   0x080055b6   0x000000ca   Code   RO         5362    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005680   0x08005680   0x000000fc   Code   RO         5363    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x0800577c   0x0800577c   0x00000038   Code   RO         5365    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080057b4   0x080057b4   0x0000005e   Code   RO         5366    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08005812   0x08005812   0x0000008e   Code   RO         5367    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x080058a0   0x080058a0   0x0000000c   Code   RO          632    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080058ac   0x080058ac   0x0000000c   Code   RO          633    i.USART2_IRQHandler  stm32f4xx_it.o
    0x080058b8   0x080058b8   0x0000000c   Code   RO          634    i.USART3_IRQHandler  stm32f4xx_it.o
    0x080058c4   0x080058c4   0x00000002   Code   RO          635    i.UsageFault_Handler  stm32f4xx_it.o
    0x080058c6   0x080058c6   0x00000002   PAD
    0x080058c8   0x080058c8   0x00000258   Code   RO         1790    i.Voltage_Step_Down  hmi_key_handler.o
    0x08005b20   0x08005b20   0x00000258   Code   RO         1791    i.Voltage_Step_Up   hmi_key_handler.o
    0x08005d78   0x08005d78   0x00000028   Code   RO         6189    i.__0sprintf        mc_w.l(printfa.o)
    0x08005da0   0x08005da0   0x00000034   Code   RO         6192    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08005dd4   0x08005dd4   0x00000008   Code   RO         6307    i.__aeabi_errno_addr  mc_w.l(errno.o)
    0x08005ddc   0x08005ddc   0x00000180   Code   RO         5899    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08005f5c   0x08005f5c   0x0000009a   Code   RO         5939    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x08005ff6   0x08005ff6   0x0000003a   Code   RO         5927    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08006030   0x08006030   0x00000014   Code   RO         6253    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08006044   0x08006044   0x00000006   Code   RO         6254    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x0800604a   0x0800604a   0x00000002   PAD
    0x0800604c   0x0800604c   0x00000010   Code   RO         6256    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x0800605c   0x0800605c   0x0000000e   Code   RO         6379    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800606a   0x0800606a   0x00000002   Code   RO         6380    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800606c   0x0800606c   0x0000000e   Code   RO         6381    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800607a   0x0800607a   0x00000002   PAD
    0x0800607c   0x0800607c   0x0000000c   Code   RO         6309    i.__set_errno       mc_w.l(errno.o)
    0x08006088   0x08006088   0x00000184   Code   RO         6194    i._fp_digits        mc_w.l(printfa.o)
    0x0800620c   0x0800620c   0x000006b4   Code   RO         6195    i._printf_core      mc_w.l(printfa.o)
    0x080068c0   0x080068c0   0x00000024   Code   RO         6196    i._printf_post_padding  mc_w.l(printfa.o)
    0x080068e4   0x080068e4   0x0000002e   Code   RO         6197    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08006912   0x08006912   0x00000016   Code   RO         6198    i._snputc           mc_w.l(printfa.o)
    0x08006928   0x08006928   0x0000000a   Code   RO         6199    i._sputc            mc_w.l(printfa.o)
    0x08006932   0x08006932   0x00000002   PAD
    0x08006934   0x08006934   0x000000cc   Code   RO         1364    i.calculate_fft_spectrum  my_fft.o
    0x08006a00   0x08006a00   0x00000064   Code   RO         1296    i.dac_calibration_get_dac_value  dac_amplitude_calibration.o
    0x08006a64   0x08006a64   0x00000048   Code   RO         1298    i.dac_calibration_init  dac_amplitude_calibration.o
    0x08006aac   0x08006aac   0x000000c4   Code   RO         1299    i.dac_calibration_interpolate_dac_value  dac_amplitude_calibration.o
    0x08006b70   0x08006b70   0x00000044   Code   RO         1300    i.dac_calibration_is_supported  dac_amplitude_calibration.o
    0x08006bb4   0x08006bb4   0x0000001c   Code   RO         1368    i.fft_init          my_fft.o
    0x08006bd0   0x08006bd0   0x00000030   Code   RO          753    i.findMinMax        ad_measure.o
    0x08006c00   0x08006c00   0x00000060   Code   RO         1371    i.generate_hanning_window  my_fft.o
    0x08006c60   0x08006c60   0x00000088   Code   RO         1158    i.key_proc          key_app.o
    0x08006ce8   0x08006ce8   0x00000044   Code   RO         1159    i.key_read          key_app.o
    0x08006d2c   0x08006d2c   0x000000d0   Code   RO           15    i.main              main.o
    0x08006dfc   0x08006dfc   0x00000032   Code   RO         1666    i.my_printf         my_usart.o
    0x08006e2e   0x08006e2e   0x00000002   PAD
    0x08006e30   0x08006e30   0x0000007c   Code   RO          754    i.readFIFOData      ad_measure.o
    0x08006eac   0x08006eac   0x0000000c   Code   RO         2043    i.scheduler_init    scheduler.o
    0x08006eb8   0x08006eb8   0x0000003c   Code   RO         2044    i.scheduler_run     scheduler.o
    0x08006ef4   0x08006ef4   0x000000a0   Code   RO          755    i.setSamplingFrequency  ad_measure.o
    0x08006f94   0x08006f94   0x00000124   Code   RO          756    i.vpp_adc_parallel  ad_measure.o
    0x080070b8   0x080070b8   0x000007bc   Data   RO         1302    .constdata          dac_amplitude_calibration.o
    0x08007874   0x08007874   0x00000008   Data   RO         2994    .constdata          stm32f4xx_hal_dma.o
    0x0800787c   0x0800787c   0x00000010   Data   RO         5681    .constdata          system_stm32f4xx.o
    0x0800788c   0x0800788c   0x00000008   Data   RO         5682    .constdata          system_stm32f4xx.o
    0x08007894   0x08007894   0x00000040   Data   RO         5902    .constdata          m_wm.l(log10f.o)
    0x080078d4   0x080078d4   0x00000081   Data   RO         6339    .constdata          mc_w.l(ctype_o.o)
    0x08007955   0x08007955   0x00000003   PAD
    0x08007958   0x08007958   0x00000004   Data   RO         6340    .constdata          mc_w.l(ctype_o.o)
    0x0800795c   0x0800795c   0x000000ab   Data   RO         1793    .conststring        hmi_key_handler.o
    0x08007a07   0x08007a07   0x00000001   PAD
    0x08007a08   0x08007a08   0x00000800   Data   RO         5780    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08008208   0x08008208   0x00000804   Data   RO         5892    .rodata.sinTable_f32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08008a0c   0x08008a0c   0x00008000   Data   RO         5798    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08010a0c   0x08010a0c   0x00000020   Data   RO         6377    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08010b08, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08010a2c, Size: 0x000086a0, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08010a2c   0x00000008   Data   RW          477    .data               fmc.o
    0x20000008   0x08010a34   0x00000010   Data   RW          758    .data               ad_measure.o
    0x20000018   0x08010a44   0x00000021   Data   RW          849    .data               ad9959.o
    0x20000039   0x08010a65   0x00000003   PAD
    0x2000003c   0x08010a68   0x00000010   Data   RW         1161    .data               key_app.o
    0x2000004c   0x08010a78   0x00000001   Data   RW         1218    .data               adc_app.o
    0x2000004d   0x08010a79   0x00000001   PAD
    0x2000004e   0x08010a7a   0x00000002   Data   RW         1259    .data               dac_app.o
    0x20000050   0x08010a7c   0x0000000c   Data   RW         1303    .data               dac_amplitude_calibration.o
    0x2000005c   0x08010a88   0x00000010   Data   RW         1668    .data               my_usart.o
    0x2000006c   0x08010a98   0x00000002   Data   RW         1719    .data               my_usart_pack.o
    0x2000006e   0x08010a9a   0x00000002   PAD
    0x20000070   0x08010a9c   0x00000030   Data   RW         1794    .data               hmi_key_handler.o
    0x200000a0   0x08010acc   0x00000028   Data   RW         2047    .data               scheduler.o
    0x200000c8   0x08010af4   0x0000000c   Data   RW         3454    .data               stm32f4xx_hal.o
    0x200000d4   0x08010b00   0x00000004   Data   RW         5683    .data               system_stm32f4xx.o
    0x200000d8   0x08010b04   0x00000004   Data   RW         6310    .data               mc_w.l(errno.o)
    0x200000dc        -       0x000000a8   Zero   RW          367    .bss                adc.o
    0x20000184        -       0x00000014   Zero   RW          409    .bss                dac.o
    0x20000198        -       0x00000050   Zero   RW          476    .bss                fmc.o
    0x200001e8        -       0x00000090   Zero   RW          524    .bss                tim.o
    0x20000278        -       0x000000d8   Zero   RW          573    .bss                usart.o
    0x20000350        -       0x00003000   Zero   RW          757    .bss                ad_measure.o
    0x20003350        -       0x00000018   Zero   RW         1068    .bss                da_output.o
    0x20003368        -       0x0000403c   Zero   RW         1381    .bss                my_fft.o
    0x200073a4        -       0x00000200   Zero   RW         1667    .bss                my_usart.o
    0x200075a4        -       0x00000034   Zero   RW         1718    .bss                my_usart_pack.o
    0x200075d8        -       0x00000ca0   Zero   RW         1792    .bss                hmi_key_handler.o
    0x20008278        -       0x00000024   Zero   RW         2099    .bss                app_pid.o
    0x2000829c   0x08010b08   0x00000004   PAD
    0x200082a0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       668         92          0         33          0      13896   ad9959.o
       624         52          0         16      12288       4990   ad_measure.o
       268         36          0          0        168       1787   adc.o
        28          8          0          1          0       1473   adc_app.o
        80         20          0          0         36        744   app_pid.o
       212          0          0          0          0       4229   cmd_to_fun.o
       312         32          0          0         24       2721   da_output.o
       156         22          0          0         20       1679   dac.o
       436         84       1980         12          0       6222   dac_amplitude_calibration.o
       124         38          0          2          0       1360   dac_app.o
        44          4          0          0          0        778   dma.o
       284         34          0          8         80       2250   fmc.o
       348         30          0          0          0       1167   gpio.o
      5852       2510        171         48       3232      14390   hmi_key_handler.o
       204         72          0         16          0       1618   key_app.o
       372         84          0          0          0     805416   main.o
       328         36          0          0      16444       3774   my_fft.o
       124         18          0          0          0        758   my_hmi.o
       414         36          0         16        512       3740   my_usart.o
       226         10          0          2         52       2549   my_usart_pack.o
        72          8          0         40          0       2298   scheduler.o
        36          8        428          0       1024        836   startup_stm32f429xx.o
       188         30          0         12          0       9573   stm32f4xx_hal.o
      1224         28          0          0          0       5882   stm32f4xx_hal_adc.o
         2          0          0          0          0       1009   stm32f4xx_hal_adc_ex.o
       208         16          0          0          0      33547   stm32f4xx_hal_cortex.o
       308          0          0          0          0       3926   stm32f4xx_hal_dac.o
      1104         18          8          0          0       5835   stm32f4xx_hal_dma.o
       616         24          0          0          0       3079   stm32f4xx_hal_gpio.o
        48          6          0          0          0        862   stm32f4xx_hal_msp.o
       124         14          0          0          0       1308   stm32f4xx_hal_pwr_ex.o
      1768        116          0          0          0       8203   stm32f4xx_hal_rcc.o
        94          0          0          0          0       1240   stm32f4xx_hal_sram.o
       654         22          0          0          0       5929   stm32f4xx_hal_tim.o
       154          0          0          0          0       1440   stm32f4xx_hal_tim_ex.o
      1824          8          0          0          0      12824   stm32f4xx_hal_uart.o
        84         30          0          0          0       6549   stm32f4xx_it.o
       330          8          0          0          0       4500   stm32f4xx_ll_fmc.o
        16          4         24          4          0       1135   system_stm32f4xx.o
       240         30          0          0        144       2303   tim.o
       452         50          0          0        216       3168   usart.o

    ----------------------------------------------------------------------
     20676       <USER>       <GROUP>        216      34244     990987   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        26          0          1          6          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      36868          0          0       6294   arm_common_tables.o
       152         12          0          0          0       1104   arm_cos_f32.o
        42         12          0          0          0        348   funder.o
       384         52         64          0          0        140   log10f.o
       154          0          0          0          0        140   roundf.o
        58          0          0          0          0        136   sqrtf.o
        28          0          0          0          0         68   _chval.o
       158          0          0          0          0         92   _strtoul.o
        26          0          0          0          0         80   atoi.o
         8          4        133          0          0         68   ctype_o.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        20         10          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2310         92          0          0          0        680   printfa.o
       112          0          0          0          0         88   strtol.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        44          0          0          0          0         68   ffixul.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      7752        <USER>      <GROUP>          4          0      27555   Library Totals
        14          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2642         32      36868          0          0      23771   arm_cortexM4lf_math.lib
       638         64         64          0          0        764   m_wm.l
      3064        122        133          4          0       1832   mc_w.l
      1394          0          0          0          0       1188   mf_w.l

    ----------------------------------------------------------------------
      7752        <USER>      <GROUP>          4          0      27555   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     28428       3856      39712        220      34244    1000170   Grand Totals
     28428       3856      39712        220      34244    1000170   ELF Image Totals
     28428       3856      39712        220          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                68140 (  66.54kB)
    Total RW  Size (RW Data + ZI Data)             34464 (  33.66kB)
    Total ROM Size (Code + RO Data + RW Data)      68360 (  66.76kB)

==============================================================================

