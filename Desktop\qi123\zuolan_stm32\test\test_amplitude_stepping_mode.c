/**
 * @file test_amplitude_stepping_mode.c
 * @brief 幅度步进模式功能测试
 * @details 测试新增的幅度步进模式功能，包括进入/退出模式、幅度调节等
 * <AUTHOR>
 * @date 2025-08-02
 */

#include "test_amplitude_stepping_mode.h"
#include "hmi_key_handler.h"
#include "my_usart.h"
#include "dac_amplitude_calibration.h"
#include "AD9959.h"
#include "stm32f4xx_hal.h"

/**
 * @brief 测试幅度步进模式基本功能
 * @details 测试进入/退出幅度步进模式的基本功能
 * @param None
 * @retval None
 */
void test_amplitude_stepping_basic(void)
{
    my_printf(&huart1, "\r\n=== 幅度步进模式基本功能测试 ===\r\n");

    // 初始化校准模块
    dac_calibration_init();

    // 显示初始状态
    my_printf(&huart1, "\r\n1. 显示初始状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();

    // 设置一个支持的频率
    my_printf(&huart1, "\r\n2. 设置DDS频率为1000Hz:\r\n");
    HMI_Set_DDS_Frequency(1000);
    my_printf(&huart1, "当前DDS频率: %luHz\r\n", HMI_Get_DDS_Frequency());

    // 测试进入幅度步进模式
    my_printf(&huart1, "\r\n3. 测试进入幅度步进模式:\r\n");
    // 模拟按键C
    extern void HMI_Key_Process(void);
    extern unsigned char USART_RX_BUF[];
    extern unsigned short USART_RX_STA;

    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001; // 设置接收完成标志和长度
    HMI_Key_Process();

    HAL_Delay(1000);

    // 显示进入后的状态
    my_printf(&huart1, "\r\n4. 显示进入后的状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();

    // 测试幅度步进增加
    my_printf(&huart1, "\r\n5. 测试幅度步进增加(按键8):\r\n");
    for (int i = 0; i < 3; i++) {
        USART_RX_BUF[0] = '8';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
    }

    // 测试幅度步进减少
    my_printf(&huart1, "\r\n6. 测试幅度步进减少(按键9):\r\n");
    for (int i = 0; i < 2; i++) {
        USART_RX_BUF[0] = '9';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
    }

    // 显示当前状态
    my_printf(&huart1, "\r\n7. 显示当前状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();

    // 测试退出幅度步进模式
    my_printf(&huart1, "\r\n8. 测试退出幅度步进模式(按键D):\r\n");
    USART_RX_BUF[0] = 'D';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();

    HAL_Delay(1000);

    // 显示退出后的状态
    my_printf(&huart1, "\r\n9. 显示退出后的状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();

    my_printf(&huart1, "\r\n=== 幅度步进模式基本功能测试完成 ===\r\n");
}

/**
 * @brief 测试不同频率下的幅度步进
 * @details 测试在不同频率下进入幅度步进模式的效果
 * @param None
 * @retval None
 */
void test_amplitude_stepping_frequencies(void)
{
    my_printf(&huart1, "\r\n=== 不同频率幅度步进测试 ===\r\n");

    // 测试频率列表
    uint32_t test_frequencies[] = {100, 500, 1000, 1500, 2000, 2500, 3000, 5000};
    int num_tests = sizeof(test_frequencies) / sizeof(test_frequencies[0]);

    for (int i = 0; i < num_tests; i++) {
        my_printf(&huart1, "\r\n--- 测试频率: %luHz ---\r\n", test_frequencies[i]);

        // 设置频率
        HMI_Set_DDS_Frequency(test_frequencies[i]);
        HAL_Delay(100);

        // 尝试进入幅度步进模式
        USART_RX_BUF[0] = 'C';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();

        HAL_Delay(500);

        // 如果成功进入，测试一次步进
        if (HMI_Get_Amplitude_Stepping_Mode()) {
            my_printf(&huart1, "✓ 成功进入幅度步进模式\r\n");

            // 测试一次增加步进
            USART_RX_BUF[0] = '8';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(200);

            // 退出模式
            USART_RX_BUF[0] = 'D';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(200);
        } else {
            my_printf(&huart1, "✗ 无法进入幅度步进模式(频率不支持)\r\n");
        }
    }

    my_printf(&huart1, "\r\n=== 不同频率幅度步进测试完成 ===\r\n");
}

/**
 * @brief 测试幅度步进边界条件
 * @details 测试幅度步进的上下限边界条件
 * @param None
 * @retval None
 */
void test_amplitude_stepping_boundaries(void)
{
    my_printf(&huart1, "\r\n=== 幅度步进边界条件测试 ===\r\n");

    // 设置支持的频率
    HMI_Set_DDS_Frequency(1000);

    // 进入幅度步进模式
    my_printf(&huart1, "进入幅度步进模式...\r\n");
    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(500);

    if (!HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "错误: 无法进入幅度步进模式\r\n");
        return;
    }

    // 测试上限边界 - 连续增加到最大值
    my_printf(&huart1, "\r\n1. 测试上限边界(连续增加到2.0V):\r\n");
    for (int i = 0; i < 15; i++) { // 从1.0V增加到2.0V需要10步，多测试几步
        my_printf(&huart1, "第%d次增加步进...\r\n", i+1);
        USART_RX_BUF[0] = '8';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(300);
    }

    // 测试下限边界 - 连续减少到最小值
    my_printf(&huart1, "\r\n2. 测试下限边界(连续减少到1.0V):\r\n");
    for (int i = 0; i < 15; i++) { // 从2.0V减少到1.0V需要10步，多测试几步
        my_printf(&huart1, "第%d次减少步进...\r\n", i+1);
        USART_RX_BUF[0] = '9';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(300);
    }

    // 显示最终状态
    my_printf(&huart1, "\r\n3. 显示最终状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();

    // 退出模式
    my_printf(&huart1, "\r\n4. 退出幅度步进模式:\r\n");
    USART_RX_BUF[0] = 'D';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();

    my_printf(&huart1, "\r\n=== 幅度步进边界条件测试完成 ===\r\n");
}

/**
 * @brief 测试按键C的查表和DAC修改功能
 * @details 专门测试按键C进入模式时的查表和DAC输出修改功能
 * @param None
 * @retval None
 */
void test_key_c_lookup_and_dac_modification(void)
{
    my_printf(&huart1, "\r\n=== 按键C查表和DAC修改功能测试 ===\r\n");

    // 初始化校准模块
    dac_calibration_init();

    // 测试不同频率下的查表和DAC修改
    uint32_t test_frequencies[] = {500, 1000, 1500, 2000, 2500};
    int num_tests = sizeof(test_frequencies) / sizeof(test_frequencies[0]);

    for (int i = 0; i < num_tests; i++) {
        my_printf(&huart1, "\r\n--- 测试频率: %luHz ---\r\n", test_frequencies[i]);

        // 设置DDS频率
        HMI_Set_DDS_Frequency(test_frequencies[i]);
        my_printf(&huart1, "DDS频率已设置为: %luHz\r\n", HMI_Get_DDS_Frequency());

        // 记录进入前的DAC状态
        uint16_t initial_dac = HMI_Get_DAC_Voltage();
        my_printf(&huart1, "进入前DAC电压: %umV\r\n", initial_dac);

        // 模拟按键C按下
        my_printf(&huart1, "模拟按键C按下...\r\n");
        extern unsigned char USART_RX_BUF[];
        extern unsigned short USART_RX_STA;

        USART_RX_BUF[0] = 'C';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();

        HAL_Delay(1000);

        // 检查是否成功进入模式并修改了DAC
        if (HMI_Get_Amplitude_Stepping_Mode()) {
            uint16_t new_dac = HMI_Get_DAC_Voltage();
            uint16_t new_dac_raw = HMI_Get_Stepping_DAC_Value();
            my_printf(&huart1, "✓ 成功进入幅度步进模式\r\n");
            my_printf(&huart1, "  步进频率: %uHz\r\n", HMI_Get_Stepping_Frequency());
            my_printf(&huart1, "  当前幅度: %u.%uV\r\n",
                     HMI_Get_Stepping_Amplitude()/10, HMI_Get_Stepping_Amplitude()%10);
            my_printf(&huart1, "  DAC原始值: %u\r\n", new_dac_raw);
            my_printf(&huart1, "  DAC电压变化: %umV → %umV\r\n", initial_dac, new_dac);

            if (new_dac != initial_dac) {
                my_printf(&huart1, "✓ DAC输出已成功修改\r\n");
            } else {
                my_printf(&huart1, "⚠ DAC输出未发生变化\r\n");
            }

            // 退出模式
            USART_RX_BUF[0] = 'D';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(500);

        } else {
            my_printf(&huart1, "✗ 无法进入幅度步进模式(频率不支持)\r\n");
        }
    }

    my_printf(&huart1, "\r\n=== 按键C查表和DAC修改功能测试完成 ===\r\n");
}

/**
 * @brief 测试DAC值的精确性
 * @details 验证查表得到的DAC值是否与预期一致
 * @param None
 * @retval None
 */
void test_dac_value_accuracy(void)
{
    my_printf(&huart1, "\r\n=== DAC值精确性测试 ===\r\n");

    // 初始化校准模块
    dac_calibration_init();

    // 测试已知的校准点
    struct {
        uint16_t frequency;
        uint16_t amplitude;
        uint16_t expected_dac;
    } test_points[] = {
        {1000, 10, 2378},  // 1000Hz, 1.0V
        {1000, 15, 2445},  // 1000Hz, 1.5V
        {1000, 20, 2494},  // 1000Hz, 2.0V
        {1500, 10, 2502},  // 1500Hz, 1.0V
        {2000, 10, 2527},  // 2000Hz, 1.0V
    };

    int num_tests = sizeof(test_points) / sizeof(test_points[0]);

    for (int i = 0; i < num_tests; i++) {
        my_printf(&huart1, "\r\n--- 测试点 %d ---\r\n", i+1);
        my_printf(&huart1, "频率: %uHz, 幅度: %u.%uV, 期望DAC: %u\r\n",
                 test_points[i].frequency,
                 test_points[i].amplitude/10, test_points[i].amplitude%10,
                 test_points[i].expected_dac);

        // 设置频率并进入步进模式
        HMI_Set_DDS_Frequency(test_points[i].frequency);

        USART_RX_BUF[0] = 'C';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);

        if (HMI_Get_Amplitude_Stepping_Mode()) {
            // 调节到目标幅度
            uint16_t current_amp = HMI_Get_Stepping_Amplitude();
            uint16_t target_amp = test_points[i].amplitude;

            while (current_amp < target_amp) {
                USART_RX_BUF[0] = '8';
                USART_RX_STA = 0x8001;
                HMI_Key_Process();
                HAL_Delay(200);
                current_amp = HMI_Get_Stepping_Amplitude();
            }

            while (current_amp > target_amp) {
                USART_RX_BUF[0] = '9';
                USART_RX_STA = 0x8001;
                HMI_Key_Process();
                HAL_Delay(200);
                current_amp = HMI_Get_Stepping_Amplitude();
            }

            // 检查DAC值
            uint16_t actual_dac_voltage = HMI_Get_DAC_Voltage();
            uint16_t expected_dac_voltage = (uint16_t)((float)test_points[i].expected_dac * 3300.0f / 4095.0f);

            my_printf(&huart1, "实际DAC电压: %umV\r\n", actual_dac_voltage);
            my_printf(&huart1, "期望DAC电压: %umV\r\n", expected_dac_voltage);

            if (abs((int)actual_dac_voltage - (int)expected_dac_voltage) <= 5) {
                my_printf(&huart1, "✓ DAC值精确度测试通过\r\n");
            } else {
                my_printf(&huart1, "✗ DAC值精确度测试失败\r\n");
            }

            // 退出模式
            USART_RX_BUF[0] = 'D';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(300);
        } else {
            my_printf(&huart1, "✗ 无法进入幅度步进模式\r\n");
        }
    }

    my_printf(&huart1, "\r\n=== DAC值精确性测试完成 ===\r\n");
}

/**
 * @brief 运行所有幅度步进模式测试
 * @details 运行所有相关的测试用例
 * @param None
 * @retval None
 */
void run_all_amplitude_stepping_tests(void)
{
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "开始幅度步进模式功能测试\r\n");
    my_printf(&huart1, "========================================\r\n");

    // 初始化必要的模块
    my_printf(&huart1, "初始化DDS模块...\r\n");
    AD9959_Init();

    my_printf(&huart1, "初始化DAC校准模块...\r\n");
    dac_calibration_init();

    // 运行测试
    test_amplitude_stepping_basic();
    HAL_Delay(2000);

    test_key_c_lookup_and_dac_modification();
    HAL_Delay(2000);

    test_dac_value_accuracy();
    HAL_Delay(2000);

    test_amplitude_stepping_frequencies();
    HAL_Delay(2000);

    test_amplitude_stepping_boundaries();

    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "幅度步进模式功能测试完成\r\n");
    my_printf(&huart1, "========================================\r\n");
}
