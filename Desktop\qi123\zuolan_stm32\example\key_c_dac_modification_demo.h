/**
 * @file key_c_dac_modification_demo.h
 * @brief 按键C查表和DAC修改功能演示头文件
 * @details 提供按键C查表和DAC修改功能演示的函数声明
 * <AUTHOR>
 * @date 2025-08-02
 */

#ifndef __KEY_C_DAC_MODIFICATION_DEMO_H
#define __KEY_C_DAC_MODIFICATION_DEMO_H

#include "stm32f4xx_hal.h"

/**
 * @brief 演示按键C的完整查表和DAC修改过程
 * @details 详细展示按键C按下后的每个步骤
 * @param None
 * @retval None
 */
void demo_key_c_complete_process(void);

/**
 * @brief 演示多个频率下的按键C功能
 * @details 在不同频率下演示按键C的查表和DAC修改效果
 * @param None
 * @retval None
 */
void demo_key_c_multiple_frequencies(void);

/**
 * @brief 演示DAC修改的实时效果
 * @details 展示按键C修改DAC后的实时效果和后续步进调节
 * @param None
 * @retval None
 */
void demo_dac_modification_realtime_effect(void);

/**
 * @brief 运行所有按键C功能演示
 * @details 运行所有相关的演示
 * @param None
 * @retval None
 */
void run_all_key_c_demos(void);

#endif // __KEY_C_DAC_MODIFICATION_DEMO_H
