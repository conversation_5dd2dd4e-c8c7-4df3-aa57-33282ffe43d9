/**
 * @file dac_raw_value_demo.c
 * @brief DAC原始值操作演示
 * @details 专门演示幅度步进模式中DAC原始值的变化和操作
 * <AUTHOR>
 * @date 2025-08-02
 */

#include "dac_raw_value_demo.h"
#include "hmi_key_handler.h"
#include "my_usart.h"
#include "dac_amplitude_calibration.h"
#include "AD9959.h"
#include "stm32f4xx_hal.h"

/**
 * @brief 演示DAC原始值的变化过程
 * @details 详细展示每次步进时DAC原始值的变化
 * @param None
 * @retval None
 */
void demo_dac_raw_value_changes(void)
{
    my_printf(&huart1, "\r\n=== DAC原始值变化演示 ===\r\n");
    
    // 初始化
    dac_calibration_init();
    AD9959_Init();
    
    // 设置频率
    uint32_t demo_frequency = 1000;
    HMI_Set_DDS_Frequency(demo_frequency);
    my_printf(&huart1, "设置DDS频率: %luHz\r\n", demo_frequency);
    
    // 显示进入前的状态
    uint16_t initial_dac_voltage = HMI_Get_DAC_Voltage();
    uint16_t initial_dac_raw = (uint16_t)((float)initial_dac_voltage * 4095.0f / 3300.0f);
    my_printf(&huart1, "进入前状态:\r\n");
    my_printf(&huart1, "  DAC电压: %umV\r\n", initial_dac_voltage);
    my_printf(&huart1, "  估算DAC原始值: %u\r\n", initial_dac_raw);
    
    // 进入幅度步进模式
    my_printf(&huart1, "\r\n按键C进入幅度步进模式...\r\n");
    extern unsigned char USART_RX_BUF[];
    extern unsigned short USART_RX_STA;
    
    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(1000);
    
    if (!HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "✗ 无法进入幅度步进模式\r\n");
        return;
    }
    
    // 显示进入后的DAC原始值
    uint16_t stepping_dac_raw = HMI_Get_Stepping_DAC_Value();
    uint16_t stepping_dac_voltage = HMI_Get_DAC_Voltage();
    my_printf(&huart1, "\r\n进入后状态:\r\n");
    my_printf(&huart1, "  DAC原始值: %u\r\n", stepping_dac_raw);
    my_printf(&huart1, "  DAC电压: %umV\r\n", stepping_dac_voltage);
    my_printf(&huart1, "  原始值变化: %u → %u (差值: %+d)\r\n", 
             initial_dac_raw, stepping_dac_raw, (int)stepping_dac_raw - (int)initial_dac_raw);
    
    // 演示步进过程中的DAC原始值变化
    my_printf(&huart1, "\r\n=== DAC原始值步进演示 ===\r\n");
    
    // 记录每次步进的DAC原始值变化
    for (int i = 0; i < 5; i++) {
        uint16_t before_dac_raw = HMI_Get_Stepping_DAC_Value();
        uint16_t before_dac_voltage = HMI_Get_DAC_Voltage();
        uint16_t before_amplitude = HMI_Get_Stepping_Amplitude();
        
        my_printf(&huart1, "\r\n--- 第%d次步进增加 ---\r\n", i+1);
        my_printf(&huart1, "步进前: 幅度=%u.%uV, DAC原始值=%u, DAC电压=%umV\r\n",
                 before_amplitude/10, before_amplitude%10, before_dac_raw, before_dac_voltage);
        
        // 执行步进
        USART_RX_BUF[0] = '8';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
        
        uint16_t after_dac_raw = HMI_Get_Stepping_DAC_Value();
        uint16_t after_dac_voltage = HMI_Get_DAC_Voltage();
        uint16_t after_amplitude = HMI_Get_Stepping_Amplitude();
        
        my_printf(&huart1, "步进后: 幅度=%u.%uV, DAC原始值=%u, DAC电压=%umV\r\n",
                 after_amplitude/10, after_amplitude%10, after_dac_raw, after_dac_voltage);
        my_printf(&huart1, "变化量: DAC原始值%+d, DAC电压%+dmV\r\n",
                 (int)after_dac_raw - (int)before_dac_raw,
                 (int)after_dac_voltage - (int)before_dac_voltage);
    }
    
    // 演示步进减少
    my_printf(&huart1, "\r\n=== DAC原始值步进减少演示 ===\r\n");
    
    for (int i = 0; i < 3; i++) {
        uint16_t before_dac_raw = HMI_Get_Stepping_DAC_Value();
        uint16_t before_dac_voltage = HMI_Get_DAC_Voltage();
        uint16_t before_amplitude = HMI_Get_Stepping_Amplitude();
        
        my_printf(&huart1, "\r\n--- 第%d次步进减少 ---\r\n", i+1);
        my_printf(&huart1, "步进前: 幅度=%u.%uV, DAC原始值=%u, DAC电压=%umV\r\n",
                 before_amplitude/10, before_amplitude%10, before_dac_raw, before_dac_voltage);
        
        // 执行步进
        USART_RX_BUF[0] = '9';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
        
        uint16_t after_dac_raw = HMI_Get_Stepping_DAC_Value();
        uint16_t after_dac_voltage = HMI_Get_DAC_Voltage();
        uint16_t after_amplitude = HMI_Get_Stepping_Amplitude();
        
        my_printf(&huart1, "步进后: 幅度=%u.%uV, DAC原始值=%u, DAC电压=%umV\r\n",
                 after_amplitude/10, after_amplitude%10, after_dac_raw, after_dac_voltage);
        my_printf(&huart1, "变化量: DAC原始值%+d, DAC电压%+dmV\r\n",
                 (int)after_dac_raw - (int)before_dac_raw,
                 (int)after_dac_voltage - (int)before_dac_voltage);
    }
    
    // 显示最终状态
    my_printf(&huart1, "\r\n=== 最终状态 ===\r\n");
    HMI_Show_Amplitude_Stepping_Status();
    
    // 退出模式
    my_printf(&huart1, "\r\n退出幅度步进模式...\r\n");
    USART_RX_BUF[0] = 'D';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    
    my_printf(&huart1, "\r\n=== DAC原始值变化演示完成 ===\r\n");
}

/**
 * @brief 演示不同频率下的DAC原始值
 * @details 展示在不同频率下，相同幅度对应的不同DAC原始值
 * @param None
 * @retval None
 */
void demo_dac_raw_value_different_frequencies(void)
{
    my_printf(&huart1, "\r\n=== 不同频率DAC原始值演示 ===\r\n");
    
    // 测试频率列表
    uint32_t frequencies[] = {500, 1000, 1500, 2000, 2500};
    int num_freq = sizeof(frequencies) / sizeof(frequencies[0]);
    
    // 测试幅度：1.0V, 1.5V, 2.0V
    uint16_t test_amplitudes[] = {10, 15, 20};
    int num_amp = sizeof(test_amplitudes) / sizeof(test_amplitudes[0]);
    
    my_printf(&huart1, "测试不同频率下相同幅度的DAC原始值差异\r\n");
    my_printf(&huart1, "频率\\幅度\t1.0V\t\t1.5V\t\t2.0V\r\n");
    my_printf(&huart1, "-------\t\t----\t\t----\t\t----\r\n");
    
    for (int i = 0; i < num_freq; i++) {
        my_printf(&huart1, "%luHz\t\t", frequencies[i]);
        
        for (int j = 0; j < num_amp; j++) {
            // 查询校准表
            uint16_t dac_raw = dac_calibration_get_dac_value(frequencies[i], test_amplitudes[j]);
            if (dac_raw > 0) {
                my_printf(&huart1, "%u\t\t", dac_raw);
            } else {
                my_printf(&huart1, "N/A\t\t");
            }
        }
        my_printf(&huart1, "\r\n");
    }
    
    my_printf(&huart1, "\r\n=== 不同频率DAC原始值演示完成 ===\r\n");
}

/**
 * @brief 演示DAC原始值与电压的转换关系
 * @details 展示DAC原始值和实际电压之间的转换关系
 * @param None
 * @retval None
 */
void demo_dac_raw_to_voltage_conversion(void)
{
    my_printf(&huart1, "\r\n=== DAC原始值与电压转换演示 ===\r\n");
    
    // 设置频率并进入步进模式
    HMI_Set_DDS_Frequency(1000);
    
    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    HAL_Delay(500);
    
    if (!HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "✗ 无法进入幅度步进模式\r\n");
        return;
    }
    
    my_printf(&huart1, "转换公式: 电压(mV) = DAC原始值 × 3300 / 4095\r\n");
    my_printf(&huart1, "幅度\t\tDAC原始值\t计算电压\t实际电压\t误差\r\n");
    my_printf(&huart1, "----\t\t--------\t--------\t--------\t----\r\n");
    
    // 从1.0V步进到2.0V，记录每个点的转换关系
    for (int i = 0; i < 10; i++) {
        uint16_t amplitude = HMI_Get_Stepping_Amplitude();
        uint16_t dac_raw = HMI_Get_Stepping_DAC_Value();
        uint16_t actual_voltage = HMI_Get_DAC_Voltage();
        uint16_t calculated_voltage = (uint16_t)((float)dac_raw * 3300.0f / 4095.0f);
        int16_t error = (int16_t)actual_voltage - (int16_t)calculated_voltage;
        
        my_printf(&huart1, "%u.%uV\t\t%u\t\t%umV\t\t%umV\t\t%+dmV\r\n",
                 amplitude/10, amplitude%10, dac_raw, calculated_voltage, actual_voltage, error);
        
        // 如果还没到最大值，继续步进
        if (amplitude < 20) {
            USART_RX_BUF[0] = '8';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(300);
        } else {
            break;
        }
    }
    
    // 退出模式
    USART_RX_BUF[0] = 'D';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    
    my_printf(&huart1, "\r\n=== DAC原始值与电压转换演示完成 ===\r\n");
}

/**
 * @brief 运行所有DAC原始值演示
 * @details 运行所有相关的演示
 * @param None
 * @retval None
 */
void run_all_dac_raw_value_demos(void)
{
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "DAC原始值操作演示\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 初始化
    my_printf(&huart1, "初始化系统...\r\n");
    dac_calibration_init();
    AD9959_Init();
    
    // 运行演示
    demo_dac_raw_value_changes();
    HAL_Delay(2000);
    
    demo_dac_raw_value_different_frequencies();
    HAL_Delay(2000);
    
    demo_dac_raw_to_voltage_conversion();
    
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "所有DAC原始值演示完成\r\n");
    my_printf(&huart1, "========================================\r\n");
}
