/**
 * @file dac_app.h
 * @brief DAC直流电压输出模块头文件
 * @details 提供0-3.3V直流电压输出功能
 * <AUTHOR>
 * @date 2025-08-01
 */

#ifndef __DAC_APP_H
#define __DAC_APP_H

#include "stm32f4xx_hal.h"

// --- 兼容性定义(为其他模块保留) ---
typedef enum
{
    WAVEFORM_SINE,    // 正弦波
    WAVEFORM_SQUARE,  // 方波
    WAVEFORM_TRIANGLE // 三角波
} dac_waveform_t;

// --- 函数声明 ---

/**
 * @brief 初始化DAC模块
 * @details 设置默认输出电压为0V
 */
void DAC_Init(void);

/**
 * @brief 设置DAC输出电压
 * @param voltage_mv 输出电压(mV)，范围0-3300mV
 * @return 实际设置的电压值(mV)
 */
uint16_t DAC_SetVoltage(uint16_t voltage_mv);

/**
 * @brief 获取当前输出电压
 * @return 当前输出电压(mV)
 */
uint16_t DAC_GetVoltage(void);

/**
 * @brief 直接设置DAC原始值
 * @param dac_raw_value DAC原始值(0-4095)
 * @return 实际设置的电压值(mV)
 */
uint16_t DAC_SetRawValue(uint16_t dac_raw_value);

/**
 * @brief 获取当前DAC原始值
 * @return 当前DAC原始值(0-4095)
 */
uint16_t DAC_GetRawValue(void);

#endif // __DAC_APP_H

