/**
 * @file amplitude_stepping_usage_example.h
 * @brief 幅度步进模式使用示例头文件
 * @details 提供幅度步进模式使用示例的函数声明
 * <AUTHOR>
 * @date 2025-08-02
 */

#ifndef __AMPLITUDE_STEPPING_USAGE_EXAMPLE_H
#define __AMPLITUDE_STEPPING_USAGE_EXAMPLE_H

#include "stm32f4xx_hal.h"

/**
 * @brief 幅度步进模式基本使用示例
 * @details 演示如何使用幅度步进模式进行精确的幅度调节
 * @param None
 * @retval None
 */
void amplitude_stepping_basic_example(void);

/**
 * @brief 多频率幅度步进示例
 * @details 演示在不同频率下使用幅度步进模式
 * @param None
 * @retval None
 */
void amplitude_stepping_multi_frequency_example(void);

/**
 * @brief 精确幅度设置示例
 * @details 演示如何使用幅度步进模式设置精确的输出幅度
 * @param None
 * @retval None
 */
void amplitude_stepping_precision_example(void);

/**
 * @brief 运行所有幅度步进使用示例
 * @details 运行所有的使用示例
 * @param None
 * @retval None
 */
void run_all_amplitude_stepping_examples(void);

#endif // __AMPLITUDE_STEPPING_USAGE_EXAMPLE_H
