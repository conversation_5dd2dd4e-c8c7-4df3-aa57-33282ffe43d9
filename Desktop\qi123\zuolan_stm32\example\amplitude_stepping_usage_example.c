/**
 * @file amplitude_stepping_usage_example.c
 * @brief 幅度步进模式使用示例
 * @details 展示如何使用新的幅度步进模式功能
 * <AUTHOR>
 * @date 2025-08-02
 */

#include "amplitude_stepping_usage_example.h"
#include "hmi_key_handler.h"
#include "my_usart.h"
#include "dac_amplitude_calibration.h"
#include "AD9959.h"
#include "stm32f4xx_hal.h"

/**
 * @brief 幅度步进模式基本使用示例
 * @details 演示如何使用幅度步进模式进行精确的幅度调节
 * @param None
 * @retval None
 */
void amplitude_stepping_basic_example(void)
{
    my_printf(&huart1, "\r\n=== 幅度步进模式基本使用示例 ===\r\n");
    
    // 1. 初始化必要的模块
    my_printf(&huart1, "1. 初始化模块...\r\n");
    dac_calibration_init();
    AD9959_Init();
    
    // 2. 设置DDS频率到支持的范围
    my_printf(&huart1, "2. 设置DDS频率为1000Hz...\r\n");
    HMI_Set_DDS_Frequency(1000);
    my_printf(&huart1, "   当前频率: %luHz\r\n", HMI_Get_DDS_Frequency());
    
    // 3. 显示当前状态
    my_printf(&huart1, "3. 显示当前状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();
    
    // 4. 模拟按键C进入幅度步进模式
    my_printf(&huart1, "4. 进入幅度步进模式(模拟按键C)...\r\n");
    // 这里在实际使用中是通过串口屏按键触发的
    // 为了演示，我们直接调用内部函数（实际应用中不建议这样做）
    extern unsigned char USART_RX_BUF[];
    extern unsigned short USART_RX_STA;
    
    USART_RX_BUF[0] = 'C';
    USART_RX_STA = 0x8001; // 设置接收完成标志
    HMI_Key_Process();
    
    HAL_Delay(1000);
    
    // 5. 检查是否成功进入模式
    if (HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "✓ 成功进入幅度步进模式\r\n");
        my_printf(&huart1, "   步进频率: %uHz\r\n", HMI_Get_Stepping_Frequency());
        my_printf(&huart1, "   当前幅度: %u.%uV\r\n", 
                 HMI_Get_Stepping_Amplitude()/10, HMI_Get_Stepping_Amplitude()%10);
    } else {
        my_printf(&huart1, "✗ 进入幅度步进模式失败\r\n");
        return;
    }
    
    // 6. 演示幅度调节
    my_printf(&huart1, "5. 演示幅度调节:\r\n");
    
    // 增加幅度到1.3V
    my_printf(&huart1, "   增加幅度到1.3V (按键8 x3):\r\n");
    for (int i = 0; i < 3; i++) {
        USART_RX_BUF[0] = '8';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
    }
    
    // 减少幅度到1.1V
    my_printf(&huart1, "   减少幅度到1.1V (按键9 x2):\r\n");
    for (int i = 0; i < 2; i++) {
        USART_RX_BUF[0] = '9';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
    }
    
    // 7. 显示最终状态
    my_printf(&huart1, "6. 显示最终状态:\r\n");
    HMI_Show_Amplitude_Stepping_Status();
    
    // 8. 退出幅度步进模式
    my_printf(&huart1, "7. 退出幅度步进模式(模拟按键D)...\r\n");
    USART_RX_BUF[0] = 'D';
    USART_RX_STA = 0x8001;
    HMI_Key_Process();
    
    HAL_Delay(1000);
    
    // 9. 确认退出
    if (!HMI_Get_Amplitude_Stepping_Mode()) {
        my_printf(&huart1, "✓ 成功退出幅度步进模式\r\n");
    } else {
        my_printf(&huart1, "✗ 退出幅度步进模式失败\r\n");
    }
    
    my_printf(&huart1, "\r\n=== 幅度步进模式基本使用示例完成 ===\r\n");
}

/**
 * @brief 多频率幅度步进示例
 * @details 演示在不同频率下使用幅度步进模式
 * @param None
 * @retval None
 */
void amplitude_stepping_multi_frequency_example(void)
{
    my_printf(&huart1, "\r\n=== 多频率幅度步进示例 ===\r\n");
    
    // 测试频率列表（选择校准表中支持的频率）
    uint32_t frequencies[] = {500, 1000, 1500, 2000, 2500};
    int num_freq = sizeof(frequencies) / sizeof(frequencies[0]);
    
    for (int i = 0; i < num_freq; i++) {
        my_printf(&huart1, "\r\n--- 测试频率: %luHz ---\r\n", frequencies[i]);
        
        // 设置频率
        HMI_Set_DDS_Frequency(frequencies[i]);
        HAL_Delay(200);
        
        // 进入幅度步进模式
        USART_RX_BUF[0] = 'C';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(500);
        
        if (HMI_Get_Amplitude_Stepping_Mode()) {
            my_printf(&huart1, "✓ 频率%luHz支持幅度步进\r\n", frequencies[i]);
            
            // 测试一次幅度调节
            my_printf(&huart1, "  测试幅度调节: 1.0V → 1.2V\r\n");
            USART_RX_BUF[0] = '8'; // +0.1V
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(200);
            
            USART_RX_BUF[0] = '8'; // +0.1V
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(200);
            
            my_printf(&huart1, "  当前幅度: %u.%uV\r\n", 
                     HMI_Get_Stepping_Amplitude()/10, HMI_Get_Stepping_Amplitude()%10);
            
            // 退出模式
            USART_RX_BUF[0] = 'D';
            USART_RX_STA = 0x8001;
            HMI_Key_Process();
            HAL_Delay(200);
        } else {
            my_printf(&huart1, "✗ 频率%luHz不支持幅度步进\r\n", frequencies[i]);
        }
    }
    
    my_printf(&huart1, "\r\n=== 多频率幅度步进示例完成 ===\r\n");
}

/**
 * @brief 精确幅度设置示例
 * @details 演示如何使用幅度步进模式设置精确的输出幅度
 * @param None
 * @retval None
 */
void amplitude_stepping_precision_example(void)
{
    my_printf(&huart1, "\r\n=== 精确幅度设置示例 ===\r\n");
    
    // 设置频率
    HMI_Set_DDS_Frequency(1000);
    
    // 目标幅度列表
    float target_amplitudes[] = {1.2f, 1.5f, 1.8f, 1.3f};
    int num_targets = sizeof(target_amplitudes) / sizeof(target_amplitudes[0]);
    
    for (int i = 0; i < num_targets; i++) {
        my_printf(&huart1, "\r\n--- 设置目标幅度: %.1fV ---\r\n", target_amplitudes[i]);
        
        // 进入幅度步进模式
        USART_RX_BUF[0] = 'C';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(300);
        
        if (!HMI_Get_Amplitude_Stepping_Mode()) {
            my_printf(&huart1, "✗ 无法进入幅度步进模式\r\n");
            continue;
        }
        
        // 计算需要的步进次数（从1.0V开始）
        float current_amplitude = 1.0f;
        float target = target_amplitudes[i];
        
        my_printf(&huart1, "从%.1fV调节到%.1fV\r\n", current_amplitude, target);
        
        if (target > current_amplitude) {
            // 需要增加
            int steps = (int)((target - current_amplitude) / 0.1f + 0.5f); // 四舍五入
            my_printf(&huart1, "需要增加%d步(每步0.1V)\r\n", steps);
            
            for (int j = 0; j < steps; j++) {
                USART_RX_BUF[0] = '8';
                USART_RX_STA = 0x8001;
                HMI_Key_Process();
                HAL_Delay(200);
            }
        } else if (target < current_amplitude) {
            // 需要减少
            int steps = (int)((current_amplitude - target) / 0.1f + 0.5f); // 四舍五入
            my_printf(&huart1, "需要减少%d步(每步0.1V)\r\n", steps);
            
            for (int j = 0; j < steps; j++) {
                USART_RX_BUF[0] = '9';
                USART_RX_STA = 0x8001;
                HMI_Key_Process();
                HAL_Delay(200);
            }
        }
        
        // 显示最终结果
        uint16_t final_amplitude = HMI_Get_Stepping_Amplitude();
        my_printf(&huart1, "✓ 最终幅度: %u.%uV\r\n", 
                 final_amplitude/10, final_amplitude%10);
        
        // 退出模式
        USART_RX_BUF[0] = 'D';
        USART_RX_STA = 0x8001;
        HMI_Key_Process();
        HAL_Delay(300);
    }
    
    my_printf(&huart1, "\r\n=== 精确幅度设置示例完成 ===\r\n");
}

/**
 * @brief 运行所有幅度步进使用示例
 * @details 运行所有的使用示例
 * @param None
 * @retval None
 */
void run_all_amplitude_stepping_examples(void)
{
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "幅度步进模式使用示例\r\n");
    my_printf(&huart1, "========================================\r\n");
    
    // 初始化
    my_printf(&huart1, "初始化系统...\r\n");
    dac_calibration_init();
    AD9959_Init();
    
    // 运行示例
    amplitude_stepping_basic_example();
    HAL_Delay(2000);
    
    amplitude_stepping_multi_frequency_example();
    HAL_Delay(2000);
    
    amplitude_stepping_precision_example();
    
    my_printf(&huart1, "\r\n========================================\r\n");
    my_printf(&huart1, "所有幅度步进使用示例完成\r\n");
    my_printf(&huart1, "========================================\r\n");
}
